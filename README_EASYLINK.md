# EasyLink SDK Module

This Laravel application includes a fully configured EasyLink SDK module for communicating with Fingerspot EasyLink devices.

## Configuration

The EasyLink SDK is configured through environment variables in your `.env` file:

```env
EASYLINK_SDK_HOST=http://**************:5005
EASYLINK_SDK_SN=66208023321907
EASYLINK_SERVER_PORT=7005
EASYLINK_DB_HOST=localhost
EASYLINK_DB_DATABASE=fin_pro_test
```

## Usage

### Basic Usage with Facade

```php
use Kangangga\EasylinkSdk\EasylinkSdkFacade as EasylinkSdk;

// Get device information
$deviceInfo = EasylinkSdk::device();

// Get new attendance logs
$attendanceLogs = EasylinkSdk::scanlogNew();
```

### Available Methods

#### Device Information
```php
$deviceInfo = EasylinkSdk::device();
```
Returns device information from the EasyLink device.

#### Attendance Logs
```php
// Get new attendance logs
$attendanceLogs = EasylinkSdk::scanlogNew();

// Get all attendance logs (with paging)
$allLogs = EasylinkSdk::scanlogAll();
```

#### User Management
```php
use Kangangga\EasylinkSdk\Dto\EasylinkUser;

// Create a new user
$user = new EasylinkUser([
    'pin' => '12345',
    'nama' => 'John Doe',
    'pwd' => 'password',
    'rfid' => '1234567890',
    'priv' => 0,
    'tmp' => 'template_data'
]);

$result = EasylinkSdk::userSet($user);

// Delete users
$result = EasylinkSdk::userDel();
```

## Testing

### Command Line Testing
Use the built-in test command to verify your configuration:

```bash
php artisan easylink:test
```

This command will:
- Display your current configuration
- Test device connectivity
- Test attendance log retrieval
- Show detailed error messages if any issues occur

### Web Testing
Visit these test routes in your browser:

- `/test-easylink-device` - Test device information retrieval
- `/test-easylink-attendance` - Test attendance log retrieval

Both routes return JSON responses with success/error status and configuration details.

### Controller Usage
The `EasylinkController` provides ready-to-use endpoints:

```php
// app/Http/Controllers/EasylinkController.php
class EasylinkController extends Controller
{
    public function getDeviceInfo()
    {
        $deviceInfo = EasylinkSdk::device();
        return response()->json($deviceInfo);
    }

    public function getAttendanceLogs()
    {
        $attendanceLogs = EasylinkSdk::scanlogNew();
        return response()->json($attendanceLogs);
    }
}
```

## Configuration Files

### Main Configuration
- `config/easylink.php` - Main configuration file
- `app/Providers/EasylinkServiceProvider.php` - Service provider for dependency injection

### Environment Variables
All configuration is driven by environment variables:

- `EASYLINK_SDK_HOST` - EasyLink device host URL
- `EASYLINK_SDK_SN` - Device serial number
- `EASYLINK_SERVER_PORT` - Server port (optional)
- `EASYLINK_DB_HOST` - Database host for EasyLink data
- `EASYLINK_DB_DATABASE` - Database name for EasyLink data

## Package Information

This module uses the `kangangga/laravel-easylink` package:
- **Package**: kangangga/laravel-easylink
- **Version**: dev-main
- **Repository**: https://github.com/kangangga/laravel-easylink
- **License**: MIT

## Troubleshooting

### Connection Issues
If you encounter connection timeouts or errors:

1. Verify the device IP address and port in your `.env` file
2. Ensure the EasyLink device is powered on and connected to the network
3. Check network connectivity between your server and the device
4. Verify the serial number matches your device

### Configuration Issues
If the SDK doesn't load your configuration:

1. Clear the configuration cache: `php artisan config:clear`
2. Verify your `.env` file contains all required variables
3. Check that the `EasylinkServiceProvider` is registered in `config/app.php`

### Testing
Run the test command to diagnose issues:
```bash
php artisan easylink:test
```

This will show you exactly what configuration is being used and where the connection fails.
