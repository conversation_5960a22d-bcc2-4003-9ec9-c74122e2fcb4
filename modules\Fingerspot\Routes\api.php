<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\Fingerspot\Http\Controllers\FingerspotApiController;
use Modules\Fingerspot\Http\Controllers\FingerspotWebhookController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your Fingerspot module.
| These routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group.
|
*/

Route::prefix('fingerspot')->name('fingerspot.api.')->middleware('api')->group(function () {
    
    // Device Management
    Route::prefix('devices')->name('devices.')->group(function () {
        Route::get('/', [FingerspotApiController::class, 'getDevices'])->name('list');
        Route::get('/{deviceId}', [FingerspotApiController::class, 'getDevice'])->name('show');
        Route::get('/{deviceId}/status', [FingerspotApiController::class, 'getDeviceStatus'])->name('status');
        Route::post('/{deviceId}/command', [FingerspotApiController::class, 'sendDeviceCommand'])->name('command');
        Route::post('/{deviceId}/users/{userId}/sync', [FingerspotApiController::class, 'syncUserToDevice'])->name('sync.user');
    });

    // User/Employee Management
    Route::prefix('users')->name('users.')->group(function () {
        Route::get('/', [FingerspotApiController::class, 'getUsers'])->name('list');
        Route::post('/', [FingerspotApiController::class, 'createUser'])->name('create');
        Route::put('/{userId}', [FingerspotApiController::class, 'updateUser'])->name('update');
        Route::delete('/{userId}', [FingerspotApiController::class, 'deleteUser'])->name('delete');
    });

    // Attendance Management
    Route::prefix('attendance')->name('attendance.')->group(function () {
        Route::get('/logs', [FingerspotApiController::class, 'getAttendanceLogs'])->name('logs');
        Route::get('/test-attlog', [FingerspotApiController::class, 'testGetAttlog'])->name('test.attlog');
        Route::post('/test-attlog-save', [FingerspotApiController::class, 'testGetAttlogAndSave'])->name('test.attlog.save');
    });

    // Webhook Management
    Route::prefix('webhooks')->name('webhooks.')->group(function () {
        Route::get('/stats', [FingerspotWebhookController::class, 'getWebhookStats'])->name('stats');
        Route::get('/config', [FingerspotWebhookController::class, 'getWebhookConfiguration'])->name('config');
        Route::get('/test', [FingerspotWebhookController::class, 'testWebhookEndpoint'])->name('test');
    });

    // Configuration and Utilities
    Route::prefix('config')->name('config.')->group(function () {
        Route::get('/', [FingerspotApiController::class, 'getConfiguration'])->name('get');
        Route::get('/usage', [FingerspotApiController::class, 'getApiUsage'])->name('usage');
    });

    // Testing and Diagnostics
    Route::prefix('test')->name('test.')->group(function () {
        Route::get('/connectivity', [FingerspotApiController::class, 'testConnectivity'])->name('connectivity');
        Route::get('/webhook/{type?}', [FingerspotWebhookController::class, 'testWebhookEndpoint'])->name('webhook');
    });

    // Cache Management
    Route::prefix('cache')->name('cache.')->group(function () {
        Route::post('/clear', [FingerspotApiController::class, 'clearCache'])->name('clear');
    });

    // Health Check
    Route::get('/health', function () {
        return response()->json([
            'success' => true,
            'module' => 'Fingerspot',
            'status' => 'healthy',
            'timestamp' => now()->toDateTimeString(),
            'version' => '1.0.0',
            'features' => [
                'api' => config('fingerspot.enabled', true),
                'webhooks' => config('fingerspot.webhook.enabled', true),
                'real_time_sync' => config('fingerspot.features.real_time_sync', true),
                'device_management' => config('fingerspot.features.device_management', true),
                'employee_management' => config('fingerspot.features.employee_management', true),
            ]
        ]);
    })->name('health');

    // Module Information
    Route::get('/info', function () {
        return response()->json([
            'success' => true,
            'module' => [
                'name' => 'Fingerspot',
                'alias' => 'fingerspot',
                'description' => 'Fingerspot Developer API and Webhook Integration Module',
                'version' => '1.0.0',
                'author' => 'Laravel Attendance System',
                'routes' => [
                    'web_prefix' => 'fingerspot',
                    'api_prefix' => 'api/fingerspot',
                    'webhook_prefix' => 'webhook/fingerspot',
                    'dashboard' => url('/fingerspot'),
                ],
                'endpoints' => [
                    'api_base' => config('fingerspot.api.base_url'),
                    'webhook_endpoints' => config('fingerspot.webhook.endpoints'),
                ],
                'features' => config('fingerspot.features'),
            ],
            'timestamp' => now()->toDateTimeString()
        ]);
    })->name('info');
});
