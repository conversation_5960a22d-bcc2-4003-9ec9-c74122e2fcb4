<?php

namespace Modules\EasyLinkYuswa\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Config;
use Modules\EasyLinkYuswa\Services\EasyLinkService;
use Modules\EasyLinkYuswa\Console\TestEasyLinkCommand;

class EasyLinkYuswaServiceProvider extends ServiceProvider
{
    protected $moduleName = 'EasyLinkYuswa';
    protected $moduleNameLower = 'easylinkyuswa';

    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->registerCommands();
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->app->register(RouteServiceProvider::class);
        
        // Register the main service
        $this->app->singleton(EasyLinkService::class, function () {
            return new EasyLinkService();
        });

        // Register service alias
        $this->app->alias(EasyLinkService::class, 'easylinkyuswa.service');
    }

    /**
     * Register commands.
     *
     * @return void
     */
    protected function registerCommands()
    {
        $this->commands([
            TestEasyLinkCommand::class,
        ]);
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->publishes([
            $this->getModulePath('Config/config.php') => config_path($this->moduleNameLower . '.php'),
        ], 'config');

        $this->mergeConfigFrom(
            $this->getModulePath('Config/config.php'), $this->moduleNameLower
        );
    }

    /**
     * Register views.
     *
     * @return void
     */
    public function registerViews()
    {
        $viewPath = resource_path('views/modules/' . $this->moduleNameLower);

        $sourcePath = $this->getModulePath('Resources/views');

        $this->publishes([
            $sourcePath => $viewPath
        ], ['views', $this->moduleNameLower . '-module-views']);

        $this->loadViewsFrom(array_merge($this->getPublishableViewPaths(), [$sourcePath]), $this->moduleNameLower);
    }

    /**
     * Register translations.
     *
     * @return void
     */
    public function registerTranslations()
    {
        $langPath = resource_path('lang/modules/' . $this->moduleNameLower);

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, $this->moduleNameLower);
        } else {
            $this->loadTranslationsFrom($this->getModulePath('Resources/lang'), $this->moduleNameLower);
        }
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [
            EasyLinkService::class,
            'easylinkyuswa.service'
        ];
    }

    /**
     * Get module path
     *
     * @param string $path
     * @return string
     */
    private function getModulePath(string $path = ''): string
    {
        $basePath = __DIR__ . '/..';
        return $path ? $basePath . '/' . $path : $basePath;
    }

    private function getPublishableViewPaths(): array
    {
        $paths = [];
        foreach (Config::get('view.paths') as $path) {
            if (is_dir($path . '/modules/' . $this->moduleNameLower)) {
                $paths[] = $path . '/modules/' . $this->moduleNameLower;
            }
        }
        return $paths;
    }
}
