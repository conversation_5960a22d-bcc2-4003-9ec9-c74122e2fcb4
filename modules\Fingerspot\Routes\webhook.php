<?php

use Illuminate\Support\Facades\Route;
use Modules\Fingerspot\Http\Controllers\FingerspotWebhookController;

/*
|--------------------------------------------------------------------------
| Webhook Routes
|--------------------------------------------------------------------------
|
| Here is where you can register webhook routes for your Fingerspot module.
| These routes are loaded by the RouteServiceProvider and are used to
| receive webhooks from the Fingerspot Developer API.
|
*/

Route::prefix('webhook/fingerspot')->name('fingerspot.webhook.')->group(function () {
    
    // Attendance Webhooks
    Route::post('/attendance', [FingerspotWebhookController::class, 'handleAttendanceWebhook'])
         ->name('attendance');
    
    // Employee Webhooks
    Route::post('/employee', [FingerspotWebhookController::class, 'handleEmployeeWebhook'])
         ->name('employee');
    
    // Device Webhooks
    Route::post('/device', [FingerspotWebhookController::class, 'handleDeviceWebhook'])
         ->name('device');
    
    // General Webhooks
    Route::post('/general', [FingerspotWebhookController::class, 'handleGeneralWebhook'])
         ->name('general');
    
    // Webhook verification endpoint (for testing webhook setup)
    Route::get('/verify', function () {
        return response()->json([
            'success' => true,
            'message' => 'Fingerspot webhook endpoint is active',
            'module' => 'Fingerspot',
            'timestamp' => now()->toDateTimeString(),
            'endpoints' => [
                'attendance' => url('/webhook/fingerspot/attendance'),
                'employee' => url('/webhook/fingerspot/employee'),
                'device' => url('/webhook/fingerspot/device'),
                'general' => url('/webhook/fingerspot/general'),
            ],
            'configuration' => [
                'webhook_enabled' => config('fingerspot.webhook.enabled', true),
                'signature_verification' => config('fingerspot.webhook.verify_signature', true),
                'logging_enabled' => config('fingerspot.logging.webhook_logs', true),
                'store_raw_data' => config('fingerspot.database.store_raw_data', true),
            ]
        ]);
    })->name('verify');
    
    // Health check for webhooks
    Route::get('/health', function () {
        return response()->json([
            'success' => true,
            'status' => 'healthy',
            'webhook_service' => 'active',
            'timestamp' => now()->toDateTimeString()
        ]);
    })->name('health');
});
