<?php

namespace Modules\Fingerspot\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Illuminate\View\View;
use Modules\Fingerspot\Services\FingerspotApiService;
use Modules\Fingerspot\Services\FingerspotWebhookService;
use Modules\Fingerspot\Models\WebhookLog;
use Modules\Fingerspot\Models\AttendanceLog;
use Modules\Fingerspot\Models\DeviceLog;
use Exception;

class FingerspotDashboardController extends Controller
{
    public function __construct(
        private FingerspotApiService $apiService,
        private FingerspotWebhookService $webhookService
    ) {}

    /**
     * Display the main dashboard
     */
    public function index(): View
    {
        $config = $this->apiService->getConfiguration();
        $webhookConfig = $this->webhookService->getWebhookConfiguration();
        
        return view('fingerspot::dashboard', compact('config', 'webhookConfig'));
    }

    /**
     * Display module information page
     */
    public function info(): View
    {
        $config = $this->apiService->getConfiguration();
        $webhookConfig = $this->webhookService->getWebhookConfiguration();
        $stats = $this->getModuleStats();
        
        return view('fingerspot::info', compact('config', 'webhookConfig', 'stats'));
    }

    /**
     * Display settings page
     */
    public function settings(): View
    {
        $config = $this->apiService->getConfiguration();
        $webhookConfig = $this->webhookService->getWebhookConfiguration();
        
        return view('fingerspot::settings', compact('config', 'webhookConfig'));
    }

    /**
     * Display devices page
     */
    public function devices(): View
    {
        return view('fingerspot::devices');
    }

    /**
     * Display attendance logs page
     */
    public function attendance(): View
    {
        return view('fingerspot::attendance');
    }

    /**
     * Display webhook logs page
     */
    public function webhooks(): View
    {
        return view('fingerspot::webhooks');
    }

    /**
     * Get dashboard data for AJAX requests
     */
    public function getDashboardData(): JsonResponse
    {
        try {
            $connectivity = $this->apiService->testConnectivity();
            $stats = $this->getModuleStats();
            $config = $this->apiService->getConfiguration();
            $webhookStats = $this->webhookService->getWebhookStats(1); // Last 24 hours
            
            return response()->json([
                'success' => true,
                'data' => [
                    'connectivity' => $connectivity,
                    'stats' => $stats,
                    'config' => $config,
                    'webhook_stats' => $webhookStats,
                    'timestamp' => now()->toDateTimeString()
                ]
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get dashboard data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test module functionality
     */
    public function testModule(): JsonResponse
    {
        try {
            $results = [];
            
            // Test API connectivity
            $results['api_connectivity'] = $this->apiService->testConnectivity();
            
            // Test device list
            try {
                $deviceResult = $this->apiService->getDevices(1, 5);
                $results['device_list'] = [
                    'status' => $deviceResult['success'] ? 'success' : 'failed',
                    'count' => $deviceResult['success'] ? count($deviceResult['data']['devices'] ?? []) : 0,
                    'response' => $deviceResult
                ];
            } catch (Exception $e) {
                $results['device_list'] = [
                    'status' => 'failed',
                    'error' => $e->getMessage()
                ];
            }
            
            // Test webhook configuration
            $results['webhook_config'] = $this->webhookService->getWebhookConfiguration();
            
            // Test webhook endpoint
            try {
                $webhookTest = $this->webhookService->testWebhookEndpoint('general');
                $results['webhook_test'] = [
                    'status' => 'success',
                    'result' => $webhookTest
                ];
            } catch (Exception $e) {
                $results['webhook_test'] = [
                    'status' => 'failed',
                    'error' => $e->getMessage()
                ];
            }
            
            // Overall test result
            $overallSuccess = $results['api_connectivity']['connected'] && 
                            $results['device_list']['status'] === 'success' && 
                            $results['webhook_test']['status'] === 'success';
            
            return response()->json([
                'success' => true,
                'overall_test_result' => $overallSuccess ? 'passed' : 'failed',
                'data' => $results,
                'test_timestamp' => now()->toDateTimeString()
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Module test failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear module cache
     */
    public function clearCache(): JsonResponse
    {
        try {
            // Clear API cache
            $this->apiService->clearCache();
            
            // Clear Laravel cache
            \Artisan::call('cache:clear');
            \Artisan::call('config:clear');
            \Artisan::call('route:clear');
            \Artisan::call('view:clear');
            
            return response()->json([
                'success' => true,
                'message' => 'Cache cleared successfully'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear cache: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export configuration
     */
    public function exportConfig(): JsonResponse
    {
        try {
            $config = $this->apiService->getConfiguration();
            $webhookConfig = $this->webhookService->getWebhookConfiguration();
            $stats = $this->getModuleStats();
            
            $exportData = [
                'module' => 'Fingerspot',
                'export_timestamp' => now()->toDateTimeString(),
                'api_configuration' => $config,
                'webhook_configuration' => $webhookConfig,
                'statistics' => $stats,
                'version' => '1.0.0'
            ];
            
            return response()->json([
                'success' => true,
                'data' => $exportData,
                'filename' => 'fingerspot_config_' . now()->format('Y-m-d_H-i-s') . '.json'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export configuration: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get real-time status updates
     */
    public function getRealtimeStatus(): JsonResponse
    {
        try {
            $connectivity = $this->apiService->testConnectivity();
            $recentWebhooks = WebhookLog::recent(1)->count();
            $recentAttendance = AttendanceLog::recent(1)->count();
            $recentDeviceLogs = DeviceLog::recent(1)->count();
            
            return response()->json([
                'success' => true,
                'data' => [
                    'api_connected' => $connectivity['connected'],
                    'api_status' => $connectivity['status'],
                    'api_response_time' => $connectivity['response_time'],
                    'recent_webhooks_24h' => $recentWebhooks,
                    'recent_attendance_24h' => $recentAttendance,
                    'recent_device_logs_24h' => $recentDeviceLogs,
                    'timestamp' => now()->toDateTimeString()
                ]
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get realtime status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get module statistics
     */
    protected function getModuleStats()
    {
        try {
            return [
                'total_webhooks' => WebhookLog::count(),
                'total_attendance_logs' => AttendanceLog::count(),
                'total_device_logs' => DeviceLog::count(),
                'recent_webhooks_7d' => WebhookLog::recent(7)->count(),
                'recent_attendance_7d' => AttendanceLog::recent(7)->count(),
                'recent_device_logs_7d' => DeviceLog::recent(7)->count(),
                'successful_webhooks_7d' => WebhookLog::recent(7)->successful()->count(),
                'failed_webhooks_7d' => WebhookLog::recent(7)->failed()->count(),
                'webhook_types_7d' => WebhookLog::recent(7)
                    ->groupBy('type')
                    ->selectRaw('type, count(*) as count')
                    ->pluck('count', 'type'),
                'last_webhook' => WebhookLog::latest('received_at')->first(),
                'last_attendance' => AttendanceLog::latest('timestamp')->first(),
                'last_device_log' => DeviceLog::latest('timestamp')->first(),
            ];
        } catch (Exception $e) {
            return [
                'error' => 'Failed to get statistics: ' . $e->getMessage()
            ];
        }
    }
}
