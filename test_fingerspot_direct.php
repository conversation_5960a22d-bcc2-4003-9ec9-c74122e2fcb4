<?php

// Bootstrap Laravel
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "🚀 Testing Fingerspot.io Get Attlog API with Direct Database Save...\n\n";

try {
    // Get the Fingerspot API service
    $apiService = app(\Modules\Fingerspot\Services\FingerspotApiService::class);
    
    echo "⚙️  Configuration:\n";
    $config = $apiService->getConfiguration();
    echo "   Base URL: {$config['api_base_url']}\n";
    echo "   Cloud ID: {$config['cloud_id']}\n";
    echo "   API Key Configured: " . ($config['api_key_configured'] ? '✅ Yes' : '❌ No') . "\n\n";
    
    // Test parameters
    $startDate = '2025-06-12';
    $endDate = '2025-06-14';
    $limit = 10;
    
    echo "📋 Test Parameters:\n";
    echo "   Start Date: {$startDate}\n";
    echo "   End Date: {$endDate}\n";
    echo "   Limit: {$limit}\n\n";
    
    // Test 1: Get attendance logs only
    echo "🔄 Test 1: Getting attendance logs from API...\n";
    $startTime = microtime(true);
    
    $result = $apiService->getAttendanceLogs(null, $startDate, $endDate, 1, $limit);
    
    $executionTime = round((microtime(true) - $startTime) * 1000, 2);
    
    echo "⏱️  Execution time: {$executionTime}ms\n";
    echo "📊 Result: " . ($result['success'] ? '✅ Success' : '❌ Failed') . "\n";
    
    if ($result['success']) {
        $data = $result['data'] ?? [];
        echo "📋 Records found: " . count($data) . "\n";
        
        if (!empty($data)) {
            echo "\n📊 Sample Record:\n";
            $sample = $data[0];
            foreach ($sample as $key => $value) {
                echo "   {$key}: {$value}\n";
            }
        }
    } else {
        echo "❌ Error: " . ($result['message'] ?? 'Unknown error') . "\n";
        echo "🔍 Full result: " . json_encode($result, JSON_PRETTY_PRINT) . "\n";
        exit(1);
    }
    
    echo "\n" . str_repeat("-", 50) . "\n\n";
    
    // Test 2: Get attendance logs and save to database
    echo "🔄 Test 2: Getting attendance logs and saving to database...\n";
    $startTime = microtime(true);
    
    $saveResult = $apiService->getAttendanceLogsAndSave(null, $startDate, $endDate, 1, $limit);
    
    $executionTime = round((microtime(true) - $startTime) * 1000, 2);
    
    echo "⏱️  Execution time: {$executionTime}ms\n";
    echo "📊 Result: " . ($saveResult['success'] ? '✅ Success' : '❌ Failed') . "\n";
    
    if ($saveResult['success']) {
        echo "💾 Database Save Summary:\n";
        echo "   Total Fetched: " . ($saveResult['total_fetched'] ?? 0) . "\n";
        echo "   Successfully Saved: " . ($saveResult['saved_count'] ?? 0) . "\n";
        echo "   Errors: " . count($saveResult['errors'] ?? []) . "\n";
        
        if (!empty($saveResult['errors'])) {
            echo "\n⚠️  Errors encountered:\n";
            foreach ($saveResult['errors'] as $error) {
                echo "   • {$error}\n";
            }
        }
    } else {
        echo "❌ Error: " . ($saveResult['message'] ?? 'Unknown error') . "\n";
        echo "🔍 Full result: " . json_encode($saveResult, JSON_PRETTY_PRINT) . "\n";
    }
    
    echo "\n" . str_repeat("-", 50) . "\n\n";
    
    // Test 3: Check database records
    echo "🔄 Test 3: Checking database records...\n";
    
    $recentLogs = \Modules\Fingerspot\Models\AttendanceLog::orderBy('created_at', 'desc')
        ->limit(5)
        ->get();
    
    echo "📋 Recent Database Records: " . $recentLogs->count() . "\n\n";
    
    if ($recentLogs->count() > 0) {
        foreach ($recentLogs as $index => $log) {
            echo "   Record " . ($index + 1) . ":\n";
            echo "     ID: {$log->id}\n";
            echo "     Employee ID: {$log->employee_id}\n";
            echo "     Device ID: " . ($log->device_id ?? 'N/A') . "\n";
            echo "     Timestamp: {$log->timestamp->format('Y-m-d H:i:s')}\n";
            echo "     Event Type: {$log->event_type}\n";
            echo "     Verification: {$log->verification_method}\n";
            echo "     Status: {$log->sync_status}\n";
            echo "     Created: {$log->created_at->format('Y-m-d H:i:s')}\n\n";
        }
    } else {
        echo "   No records found in database.\n";
    }
    
    // Test 4: Database statistics
    echo "📊 Database Statistics:\n";
    $totalLogs = \Modules\Fingerspot\Models\AttendanceLog::count();
    $todayLogs = \Modules\Fingerspot\Models\AttendanceLog::whereDate('created_at', today())->count();
    $syncedLogs = \Modules\Fingerspot\Models\AttendanceLog::where('sync_status', 'synced')->count();
    
    echo "   Total Logs: {$totalLogs}\n";
    echo "   Today's Logs: {$todayLogs}\n";
    echo "   Synced Logs: {$syncedLogs}\n";
    
    echo "\n✨ All tests completed successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Exception occurred: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . " (Line: " . $e->getLine() . ")\n";
    echo "🔍 Trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
