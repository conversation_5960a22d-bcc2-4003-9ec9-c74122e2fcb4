@extends('fingerspot::layouts.master')

@section('title', 'Module Information')

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-info-circle me-2"></i>
        Module Information
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportConfig()">
                <i class="fas fa-download"></i> Export Config
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshInfo()">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
        </div>
    </div>
</div>

<!-- Module Overview -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cube me-2"></i>
                    Module Overview
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Basic Information</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Name:</strong></td>
                                <td>Fingerspot</td>
                            </tr>
                            <tr>
                                <td><strong>Alias:</strong></td>
                                <td>fingerspot</td>
                            </tr>
                            <tr>
                                <td><strong>Version:</strong></td>
                                <td>1.0.0</td>
                            </tr>
                            <tr>
                                <td><strong>Description:</strong></td>
                                <td>Fingerspot Developer API and Webhook Integration Module</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Module Status</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Enabled:</strong></td>
                                <td><span class="badge bg-success">Yes</span></td>
                            </tr>
                            <tr>
                                <td><strong>API Status:</strong></td>
                                <td id="api-status-info">
                                    <span class="loading-spinner"></span> Checking...
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Webhooks:</strong></td>
                                <td id="webhook-status-info">
                                    <span class="loading-spinner"></span> Checking...
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Database:</strong></td>
                                <td><span class="badge bg-success">Connected</span></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Statistics
                </h5>
            </div>
            <div class="card-body">
                <div id="module-stats">
                    <div class="text-center">
                        <span class="loading-spinner"></span> Loading statistics...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- API Configuration -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog me-2"></i>
                    API Configuration
                </h5>
            </div>
            <div class="card-body">
                <div id="api-configuration">
                    <div class="text-center">
                        <span class="loading-spinner"></span> Loading configuration...
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-webhook me-2"></i>
                    Webhook Configuration
                </h5>
            </div>
            <div class="card-body">
                <div id="webhook-configuration">
                    <div class="text-center">
                        <span class="loading-spinner"></span> Loading configuration...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Routes Information -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-route me-2"></i>
                    Available Routes
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>Web Routes</h6>
                        <ul class="list-unstyled">
                            <li><code>/fingerspot</code> - Dashboard</li>
                            <li><code>/fingerspot/devices</code> - Devices</li>
                            <li><code>/fingerspot/attendance</code> - Attendance</li>
                            <li><code>/fingerspot/webhooks</code> - Webhooks</li>
                            <li><code>/fingerspot/settings</code> - Settings</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6>API Routes</h6>
                        <ul class="list-unstyled">
                            <li><code>/api/fingerspot/devices</code> - Device API</li>
                            <li><code>/api/fingerspot/users</code> - User API</li>
                            <li><code>/api/fingerspot/attendance</code> - Attendance API</li>
                            <li><code>/api/fingerspot/health</code> - Health Check</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6>Webhook Routes</h6>
                        <ul class="list-unstyled">
                            <li><code>/webhook/fingerspot/attendance</code></li>
                            <li><code>/webhook/fingerspot/employee</code></li>
                            <li><code>/webhook/fingerspot/device</code></li>
                            <li><code>/webhook/fingerspot/general</code></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Features -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-star me-2"></i>
                    Features
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>API Features</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Device Management
                                <span class="badge bg-success rounded-pill">Enabled</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Employee Management
                                <span class="badge bg-success rounded-pill">Enabled</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Attendance Reports
                                <span class="badge bg-success rounded-pill">Enabled</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                API Caching
                                <span class="badge bg-success rounded-pill">Enabled</span>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Webhook Features</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Real-time Sync
                                <span class="badge bg-success rounded-pill">Enabled</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Webhook Retry
                                <span class="badge bg-success rounded-pill">Enabled</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Signature Verification
                                <span class="badge bg-success rounded-pill">Enabled</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Event Broadcasting
                                <span class="badge bg-success rounded-pill">Enabled</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    refreshInfo();
});

function refreshInfo() {
    // Load API status
    $.get('{{ route("fingerspot.api.connectivity") }}')
        .done(function(response) {
            if (response.success && response.connectivity.connected) {
                $('#api-status-info').html('<span class="badge bg-success">Online</span>');
            } else {
                $('#api-status-info').html('<span class="badge bg-danger">Offline</span>');
            }
        })
        .fail(function() {
            $('#api-status-info').html('<span class="badge bg-danger">Error</span>');
        });
    
    // Load webhook status
    $.get('{{ route("fingerspot.api.webhooks.config") }}')
        .done(function(response) {
            if (response.success && response.configuration.enabled) {
                $('#webhook-status-info').html('<span class="badge bg-success">Enabled</span>');
            } else {
                $('#webhook-status-info').html('<span class="badge bg-warning">Disabled</span>');
            }
        })
        .fail(function() {
            $('#webhook-status-info').html('<span class="badge bg-danger">Error</span>');
        });
    
    // Load module statistics
    loadModuleStats();
    
    // Load configurations
    loadApiConfiguration();
    loadWebhookConfiguration();
}

function loadModuleStats() {
    $.get('{{ route("fingerspot.api.dashboard.data") }}')
        .done(function(response) {
            if (response.success) {
                const stats = response.data.stats;
                const html = `
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary">${stats.total_webhooks || 0}</h4>
                                <small class="text-muted">Total Webhooks</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">${stats.total_attendance_logs || 0}</h4>
                            <small class="text-muted">Attendance Logs</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-info">${stats.recent_webhooks_7d || 0}</h4>
                                <small class="text-muted">Last 7 Days</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning">${stats.total_device_logs || 0}</h4>
                            <small class="text-muted">Device Logs</small>
                        </div>
                    </div>
                `;
                $('#module-stats').html(html);
            }
        })
        .fail(function() {
            $('#module-stats').html('<div class="text-danger">Failed to load statistics</div>');
        });
}

function loadApiConfiguration() {
    $.get('{{ route("fingerspot.api.configuration") }}')
        .done(function(response) {
            if (response.success) {
                const config = response.configuration;
                const html = `
                    <table class="table table-sm">
                        <tr>
                            <td><strong>Base URL:</strong></td>
                            <td><small>${config.api_base_url}</small></td>
                        </tr>
                        <tr>
                            <td><strong>Version:</strong></td>
                            <td>${config.api_version}</td>
                        </tr>
                        <tr>
                            <td><strong>Timeout:</strong></td>
                            <td>${config.timeout}s</td>
                        </tr>
                        <tr>
                            <td><strong>Retry Attempts:</strong></td>
                            <td>${config.retry_attempts}</td>
                        </tr>
                        <tr>
                            <td><strong>Cache Enabled:</strong></td>
                            <td>${config.cache_enabled ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-secondary">No</span>'}</td>
                        </tr>
                        <tr>
                            <td><strong>Debug Mode:</strong></td>
                            <td>${config.debug_mode ? '<span class="badge bg-warning">Yes</span>' : '<span class="badge bg-secondary">No</span>'}</td>
                        </tr>
                    </table>
                `;
                $('#api-configuration').html(html);
            }
        })
        .fail(function() {
            $('#api-configuration').html('<div class="text-danger">Failed to load API configuration</div>');
        });
}

function loadWebhookConfiguration() {
    $.get('{{ route("fingerspot.api.webhooks.config") }}')
        .done(function(response) {
            if (response.success) {
                const config = response.configuration;
                const html = `
                    <table class="table table-sm">
                        <tr>
                            <td><strong>Enabled:</strong></td>
                            <td>${config.enabled ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-secondary">No</span>'}</td>
                        </tr>
                        <tr>
                            <td><strong>Signature Verification:</strong></td>
                            <td>${config.verify_signature ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-secondary">No</span>'}</td>
                        </tr>
                        <tr>
                            <td><strong>Secret Configured:</strong></td>
                            <td>${config.secret_configured ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-warning">No</span>'}</td>
                        </tr>
                        <tr>
                            <td><strong>Logging Enabled:</strong></td>
                            <td>${config.logging_enabled ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-secondary">No</span>'}</td>
                        </tr>
                        <tr>
                            <td><strong>Store Raw Data:</strong></td>
                            <td>${config.store_raw_data ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-secondary">No</span>'}</td>
                        </tr>
                        <tr>
                            <td><strong>Enabled Events:</strong></td>
                            <td><small>${config.enabled_events.length} events</small></td>
                        </tr>
                    </table>
                `;
                $('#webhook-configuration').html(html);
            }
        })
        .fail(function() {
            $('#webhook-configuration').html('<div class="text-danger">Failed to load webhook configuration</div>');
        });
}

function exportConfig() {
    $.get('{{ route("fingerspot.api.config.export") }}')
        .done(function(response) {
            if (response.success) {
                const dataStr = JSON.stringify(response.data, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = response.filename;
                link.click();
                URL.revokeObjectURL(url);
            } else {
                alert('Failed to export configuration: ' + response.message);
            }
        })
        .fail(function() {
            alert('Failed to export configuration. Please try again.');
        });
}
</script>
@endpush
