# EasyLink Kangangga Module

A modular Laravel implementation of the EasyLink SDK by Kangangga for Fingerspot EasyLink device integration.

## Overview

This module provides a complete, modular implementation of the EasyLink SDK with:
- **Modular Architecture**: Self-contained module structure
- **Modern Dashboard**: Beautiful web interface for testing and monitoring
- **RESTful API**: Clean API endpoints for integration
- **Real-time Testing**: Live connectivity and device testing
- **Configuration Management**: Environment-based configuration
- **Command Line Tools**: Artisan commands for testing and management

## Installation & Setup

### 1. Module Structure
The module is located in `modules/EasylinkKangangga/` with the following structure:
```
modules/EasylinkKangangga/
├── Config/
│   └── config.php              # Module configuration
├── Console/
│   └── TestEasylinkCommand.php # Artisan command
├── Http/
│   └── Controllers/            # Module controllers
├── Providers/
│   ├── EasylinkKanganggaServiceProvider.php
│   └── RouteServiceProvider.php
├── Resources/
│   └── views/                  # Module views
├── Routes/
│   ├── web.php                 # Web routes
│   └── api.php                 # API routes
├── Services/
│   └── AttendanceService.php   # Business logic
├── composer.json               # Module dependencies
└── module.json                 # Module metadata
```

### 2. Configuration
Configure the module through environment variables in your `.env` file:

```env
# EasyLink Device Configuration
EASYLINK_SDK_HOST=http://**************:5005
EASYLINK_SDK_SN=66208023321907
EASYLINK_SERVER_PORT=7005

# Database Configuration
EASYLINK_DB_HOST=localhost
EASYLINK_DB_DATABASE=fin_pro_test
EASYLINK_DB_USERNAME=root
EASYLINK_DB_PASSWORD=

# Module Settings
EASYLINK_MODULE_ENABLED=true
EASYLINK_DEBUG=false
EASYLINK_TIMEOUT=30
EASYLINK_AUTO_REFRESH=true
EASYLINK_REFRESH_INTERVAL=30
```

### 3. Service Provider Registration
The module service provider is automatically registered in `config/app.php`:
```php
Modules\EasylinkKangangga\Providers\EasylinkKanganggaServiceProvider::class,
```

## Usage

### Web Interface

#### Dashboard
Access the main dashboard at:
```
http://larattendance.me/easylinkangga
```

Features:
- **Real-time Status**: Live connection status indicator
- **Configuration Display**: Current module settings
- **Device Testing**: Test device connectivity and information
- **Attendance Logs**: Retrieve and display attendance data
- **Auto-refresh**: Automatic connectivity monitoring

#### Additional Pages
- **Module Info**: `/easylinkangga/info` - Module details and documentation
- **Settings**: `/easylinkangga/settings` - Configuration management

### API Endpoints

#### Web API (AJAX)
```
GET /easylinkangga/api/configuration      # Get module configuration
GET /easylinkangga/api/device-info        # Get device information
GET /easylinkangga/api/attendance-logs    # Get attendance logs
GET /easylinkangga/api/check-connectivity # Test device connectivity
```

#### REST API
```
GET /api/easylinkangga/device/info         # Device information
GET /api/easylinkangga/device/connectivity # Device connectivity
GET /api/easylinkangga/device/test         # Device test
GET /api/easylinkangga/attendance/logs     # Attendance logs
GET /api/easylinkangga/attendance/test     # Attendance test
GET /api/easylinkangga/configuration       # Configuration
GET /api/easylinkangga/health              # Health check
```

### Command Line Interface

#### Test Module
```bash
# Test module configuration and connectivity
php artisan easylinkangga:test

# Show configuration only
php artisan easylinkangga:test --config
```

#### Example Output
```
Testing EasyLink Kangangga Module...

Module Information:
  Name: EasylinkKangangga
  Alias: easylinkangga
  Description: EasyLink SDK Module by Kangangga - Fingerspot EasyLink Device Integration
  Enabled: Yes

Configuration:
  Host: http://**************:5005
  Serial number: 66208023321907
  Server port: 7005
  ...

✓ Device is connected and responding
✓ Device info retrieved successfully
✓ Attendance logs retrieved successfully

Module Statistics:
  Module enabled: Yes
  Device connected: Yes
  Total logs: 15
  ...
```

### Programmatic Usage

#### Using the Service
```php
use Modules\EasylinkKangangga\Services\AttendanceService;

$service = app(AttendanceService::class);

// Get device information
$deviceInfo = $service->getDeviceInfo();

// Get attendance logs
$logs = $service->getFormattedAttendanceLogs();

// Check connectivity
$status = $service->checkDeviceConnectivity();

// Get configuration
$config = $service->getConfiguration();
```

#### Using the Facade
```php
use Kangangga\EasylinkSdk\EasylinkSdkFacade as EasylinkSdk;

// Direct SDK usage (still works)
$deviceInfo = EasylinkSdk::device();
$attendanceLogs = EasylinkSdk::scanlogNew();
```

## Features

### 🎨 Modern Dashboard
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Real-time Updates**: Live status indicators and auto-refresh
- **Interactive Testing**: One-click testing of all functions
- **Beautiful UI**: Modern gradient design with smooth animations

### 🔧 Modular Architecture
- **Self-contained**: All module files in dedicated directory
- **Namespace Isolation**: `Modules\EasylinkKangangga` namespace
- **Route Prefixing**: All routes prefixed with `/easylinkangga`
- **Configuration Isolation**: Module-specific configuration

### 📡 API Integration
- **RESTful Design**: Clean, consistent API endpoints
- **JSON Responses**: Structured data format
- **Error Handling**: Comprehensive error management
- **Health Checks**: Built-in health monitoring

### 🛠 Developer Tools
- **Artisan Commands**: Command-line testing and management
- **Debug Mode**: Detailed error reporting
- **Configuration Validation**: Environment variable validation
- **Logging**: Comprehensive logging support

## Configuration Options

### Module Settings
```php
'enabled' => env('EASYLINK_MODULE_ENABLED', true),
'debug' => env('EASYLINK_DEBUG', false),
'timeout' => env('EASYLINK_TIMEOUT', 30),
'retry_attempts' => env('EASYLINK_RETRY_ATTEMPTS', 3),
```

### Dashboard Settings
```php
'dashboard' => [
    'title' => 'EasyLink SDK Dashboard',
    'auto_refresh' => env('EASYLINK_AUTO_REFRESH', true),
    'refresh_interval' => env('EASYLINK_REFRESH_INTERVAL', 30),
    'theme' => env('EASYLINK_THEME', 'default'),
],
```

### API Settings
```php
'api' => [
    'rate_limit' => env('EASYLINK_API_RATE_LIMIT', 60),
    'cache_ttl' => env('EASYLINK_CACHE_TTL', 300),
    'response_format' => env('EASYLINK_RESPONSE_FORMAT', 'json'),
],
```

## Troubleshooting

### Common Issues

1. **Module not loading**
   - Check service provider registration in `config/app.php`
   - Run `composer dump-autoload`
   - Clear configuration cache: `php artisan config:clear`

2. **Routes not working**
   - Check route registration in module's `RouteServiceProvider`
   - Verify route prefix: `/easylinkangga`
   - Run `php artisan route:list --name=easylinkangga`

3. **Configuration not loading**
   - Verify `.env` variables are set
   - Check module config file: `modules/EasylinkKangangga/Config/config.php`
   - Test with: `php artisan easylinkangga:test --config`

4. **Device connectivity issues**
   - Verify device IP and port in configuration
   - Check network connectivity to device
   - Test with: `php artisan easylinkangga:test`

## Version Information

- **Module Version**: 1.0.0
- **Package**: kangangga/laravel-easylink (dev-main)
- **Laravel**: ^10.10
- **PHP**: ^8.1

## Support

For issues and support:
1. Check the module documentation
2. Run diagnostic command: `php artisan easylinkangga:test`
3. Check logs for detailed error information
4. Verify configuration and network connectivity
