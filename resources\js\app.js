import './bootstrap';

// EasyLink SDK Test Dashboard
class EasylinkDashboard {
    constructor() {
        this.apiPrefix = '/easylink'; // Default prefix
        this.autoRefreshInterval = 30000; // Default 30 seconds
        this.init();
    }

    init() {
        this.detectModule();
        this.bindEvents();
        this.loadConfiguration();
    }

    detectModule() {
        const dashboard = document.getElementById('easylink-dashboard');
        if (dashboard && dashboard.dataset.module === 'easylinkangga') {
            this.apiPrefix = '/easylinkangga/api';
        }
    }

    bindEvents() {
        // Configuration test
        const configBtn = document.getElementById('test-config');
        if (configBtn) {
            configBtn.addEventListener('click', () => this.testConfiguration());
        }

        // Device connectivity test
        const connectivityBtn = document.getElementById('test-connectivity');
        if (connectivityBtn) {
            connectivityBtn.addEventListener('click', () => this.testConnectivity());
        }

        // Device info test
        const deviceBtn = document.getElementById('test-device');
        if (deviceBtn) {
            deviceBtn.addEventListener('click', () => this.testDeviceInfo());
        }

        // Attendance logs test
        const attendanceBtn = document.getElementById('test-attendance');
        if (attendanceBtn) {
            attendanceBtn.addEventListener('click', () => this.testAttendanceLogs());
        }

        // Auto refresh toggle
        const autoRefreshToggle = document.getElementById('auto-refresh');
        if (autoRefreshToggle) {
            autoRefreshToggle.addEventListener('change', (e) => this.toggleAutoRefresh(e.target.checked));
        }
    }

    async loadConfiguration() {
        try {
            const response = await axios.get(this.apiPrefix + '/configuration');
            this.displayConfiguration(response.data);
        } catch (error) {
            this.showError('Failed to load configuration: ' + error.message);
        }
    }

    displayConfiguration(data) {
        const container = document.getElementById('config-display');
        if (!container) return;

        const config = data.configuration;
        container.innerHTML = `
            <div class="config-item">
                <span class="config-label">Host</span>
                <span class="config-value">${config.host}</span>
            </div>
            <div class="config-item">
                <span class="config-label">Serial Number</span>
                <span class="config-value">${config.serial_number}</span>
            </div>
            <div class="config-item">
                <span class="config-label">Server Port</span>
                <span class="config-value">${config.server_port}</span>
            </div>
            <div class="config-item">
                <span class="config-label">DB Host</span>
                <span class="config-value">${config.db_host}</span>
            </div>
            <div class="config-item">
                <span class="config-label">DB Database</span>
                <span class="config-value">${config.db_database}</span>
            </div>
        `;
    }

    async testConfiguration() {
        const btn = document.getElementById('test-config');
        const container = document.getElementById('config-result');

        this.setButtonLoading(btn, true);
        this.clearResult(container);

        try {
            const response = await axios.get(this.apiPrefix + '/configuration');
            this.showSuccess(container, 'Configuration loaded successfully!');
            this.displayResponse(container, response.data);
        } catch (error) {
            this.showError(container, 'Failed to load configuration: ' + error.message);
        } finally {
            this.setButtonLoading(btn, false);
        }
    }

    async testConnectivity() {
        const btn = document.getElementById('test-connectivity');
        const container = document.getElementById('connectivity-result');
        const statusIndicator = document.getElementById('connection-status');

        this.setButtonLoading(btn, true);
        this.clearResult(container);
        this.updateConnectionStatus('testing');

        try {
            const response = await axios.get(this.apiPrefix + '/check-connectivity');
            const data = response.data;

            if (data.connected) {
                this.showSuccess(container, data.message);
                this.updateConnectionStatus('connected');
            } else {
                this.showError(container, data.message);
                this.updateConnectionStatus('disconnected');
            }

            this.displayResponse(container, data);
        } catch (error) {
            this.showError(container, 'Connection test failed: ' + error.message);
            this.updateConnectionStatus('disconnected');
        } finally {
            this.setButtonLoading(btn, false);
        }
    }

    async testDeviceInfo() {
        const btn = document.getElementById('test-device');
        const container = document.getElementById('device-result');

        this.setButtonLoading(btn, true);
        this.clearResult(container);

        try {
            const response = await axios.get(this.apiPrefix + '/device-info');
            const data = response.data;

            if (data.success) {
                this.showSuccess(container, 'Device information retrieved successfully!');
            } else {
                this.showError(container, data.message || 'Failed to get device info');
            }

            this.displayResponse(container, data);
        } catch (error) {
            this.showError(container, 'Device test failed: ' + error.message);
        } finally {
            this.setButtonLoading(btn, false);
        }
    }

    async testAttendanceLogs() {
        const btn = document.getElementById('test-attendance');
        const container = document.getElementById('attendance-result');

        this.setButtonLoading(btn, true);
        this.clearResult(container);

        try {
            const response = await axios.get(this.apiPrefix + '/attendance-logs');
            const data = response.data;

            if (data.success) {
                this.showSuccess(container, `Retrieved ${data.count || 0} attendance records`);
            } else {
                this.showError(container, data.message || 'Failed to get attendance logs');
            }

            this.displayResponse(container, data);
        } catch (error) {
            this.showError(container, 'Attendance test failed: ' + error.message);
        } finally {
            this.setButtonLoading(btn, false);
        }
    }

    setButtonLoading(button, loading) {
        if (!button) return;

        if (loading) {
            button.disabled = true;
            const originalText = button.innerHTML;
            button.dataset.originalText = originalText;
            button.innerHTML = '<div class="loading-spinner"></div> Testing...';
        } else {
            button.disabled = false;
            button.innerHTML = button.dataset.originalText || button.innerHTML;
        }
    }

    updateConnectionStatus(status) {
        const indicator = document.getElementById('connection-status');
        if (!indicator) return;

        indicator.className = 'status-indicator';

        switch (status) {
            case 'connected':
                indicator.classList.add('status-connected');
                indicator.innerHTML = '<i class="fas fa-check-circle"></i> Connected';
                break;
            case 'disconnected':
                indicator.classList.add('status-disconnected');
                indicator.innerHTML = '<i class="fas fa-times-circle"></i> Disconnected';
                break;
            case 'testing':
                indicator.classList.add('status-testing');
                indicator.innerHTML = '<div class="loading-spinner"></div> Testing...';
                break;
        }
    }

    showSuccess(container, message) {
        if (!container) return;
        const alert = document.createElement('div');
        alert.className = 'alert alert-success';
        alert.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
        container.appendChild(alert);
    }

    showError(container, message) {
        if (!container) return;
        const alert = document.createElement('div');
        alert.className = 'alert alert-error';
        alert.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
        container.appendChild(alert);
    }

    clearResult(container) {
        if (container) {
            container.innerHTML = '';
        }
    }

    displayResponse(container, data) {
        if (!container) return;

        const responseDiv = document.createElement('div');
        responseDiv.className = 'response-container';
        responseDiv.innerHTML = `
            <h4 style="margin-bottom: 0.5rem; color: var(--gray-600);">Response:</h4>
            <pre class="response-json">${JSON.stringify(data, null, 2)}</pre>
        `;
        container.appendChild(responseDiv);
    }

    toggleAutoRefresh(enabled) {
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
        }

        if (enabled) {
            this.autoRefreshInterval = setInterval(() => {
                this.testConnectivity();
            }, 30000); // Refresh every 30 seconds
        }
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('easylink-dashboard')) {
        new EasylinkDashboard();
    }
});
