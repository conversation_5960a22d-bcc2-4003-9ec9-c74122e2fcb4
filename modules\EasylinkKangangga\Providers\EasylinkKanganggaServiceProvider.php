<?php

namespace Modules\EasylinkKangangga\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Config;
use Kangangga\EasylinkSdk\EasylinkSdk;

class EasylinkKanganggaServiceProvider extends ServiceProvider
{
    protected string $moduleName = 'EasylinkKangangga';
    protected string $moduleNameLower = 'easylinkangga';

    /**
     * Boot the application events.
     */
    public function boot(): void
    {
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->registerCommands();
        $this->loadMigrationsFrom(module_path($this->moduleName, 'Database/Migrations'));
    }

    /**
     * Register the service provider.
     */
    public function register(): void
    {
        $this->app->register(RouteServiceProvider::class);
        
        // Merge the module config
        $this->mergeConfigFrom(
            module_path($this->moduleName, 'Config/config.php'), 
            $this->moduleNameLower
        );

        // Map our module config to the package's expected config structure
        Config::set('laravel-easylink.sdk.host', config('easylinkangga.sdk_host'));
        Config::set('laravel-easylink.sdk.serial_number', config('easylinkangga.sdk_sn'));
        Config::set('laravel-easylink.database.host', config('easylinkangga.db_host'));
        Config::set('laravel-easylink.database.database', config('easylinkangga.db_database'));
        Config::set('laravel-easylink.database.port', config('easylinkangga.server_port'));

        // Override the package's service binding to use our module config
        $this->app->bind('laravel-easylink', function () {
            return new EasylinkSdk(
                config('easylinkangga.sdk_host'),
                config('easylinkangga.sdk_sn'),
            );
        });

        // Register module facade
        $this->app->bind('easylinkangga', function () {
            return new EasylinkSdk(
                config('easylinkangga.sdk_host'),
                config('easylinkangga.sdk_sn'),
            );
        });
    }

    /**
     * Register config.
     */
    protected function registerConfig(): void
    {
        $this->publishes([
            module_path($this->moduleName, 'Config/config.php') => config_path($this->moduleNameLower . '.php'),
        ], 'config');
    }

    /**
     * Register views.
     */
    public function registerViews(): void
    {
        $viewPath = resource_path('views/modules/' . $this->moduleNameLower);
        $sourcePath = module_path($this->moduleName, 'Resources/views');

        $this->publishes([
            $sourcePath => $viewPath
        ], ['views', $this->moduleNameLower . '-module-views']);

        $this->loadViewsFrom(array_merge($this->getPublishableViewPaths(), [$sourcePath]), $this->moduleNameLower);
    }

    /**
     * Register translations.
     */
    public function registerTranslations(): void
    {
        $langPath = resource_path('lang/modules/' . $this->moduleNameLower);

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, $this->moduleNameLower);
            $this->loadJsonTranslationsFrom($langPath);
        } else {
            $this->loadTranslationsFrom(module_path($this->moduleName, 'Resources/lang'), $this->moduleNameLower);
            $this->loadJsonTranslationsFrom(module_path($this->moduleName, 'Resources/lang'));
        }
    }

    /**
     * Register console commands.
     */
    protected function registerCommands(): void
    {
        $this->commands([
            \Modules\EasylinkKangangga\Console\TestEasylinkCommand::class,
        ]);
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [];
    }

    private function getPublishableViewPaths(): array
    {
        $paths = [];
        foreach (Config::get('view.paths') as $path) {
            if (is_dir($path . '/modules/' . $this->moduleNameLower)) {
                $paths[] = $path . '/modules/' . $this->moduleNameLower;
            }
        }
        return $paths;
    }
}

if (!function_exists('module_path')) {
    function module_path($name, $path = ''): string
    {
        $basePath = base_path('modules' . DIRECTORY_SEPARATOR . $name);
        return $basePath . ($path ? DIRECTORY_SEPARATOR . $path : '');
    }
}
