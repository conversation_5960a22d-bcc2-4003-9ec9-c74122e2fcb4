<?php

namespace Modules\Fingerspot\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class WebhookReceived implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $type;
    public $payload;
    public $headers;
    public $timestamp;

    /**
     * Create a new event instance.
     */
    public function __construct(string $type, array $payload, array $headers)
    {
        $this->type = $type;
        $this->payload = $payload;
        $this->headers = $headers;
        $this->timestamp = now()->toDateTimeString();
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('fingerspot.webhooks'),
            new PrivateChannel('fingerspot.webhooks.' . $this->type),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'type' => $this->type,
            'payload' => $this->payload,
            'timestamp' => $this->timestamp,
            'event_name' => 'webhook.received'
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'webhook.received';
    }
}
