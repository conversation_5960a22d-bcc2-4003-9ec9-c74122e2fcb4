<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('fingerspot_device_logs', function (Blueprint $table) {
            $table->id();
            $table->string('device_id', 100)->nullable()->index();
            $table->string('event_type', 100)->default('device.status')->index(); // device.online, device.offline, device.error, device.status
            $table->string('status', 50)->default('unknown')->index(); // online, offline, error, maintenance, unknown
            $table->text('message')->nullable();
            $table->json('raw_data')->nullable();
            $table->timestamp('timestamp')->index();
            $table->timestamp('processed_at')->nullable();
            $table->string('ip_address', 45)->nullable();
            $table->string('firmware_version', 50)->nullable();
            $table->integer('battery_level')->nullable(); // 0-100
            $table->integer('memory_usage')->nullable(); // 0-100
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['device_id', 'timestamp']);
            $table->index(['event_type', 'timestamp']);
            $table->index(['status', 'timestamp']);
            $table->index(['device_id', 'status']);
            $table->index(['timestamp', 'device_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('fingerspot_device_logs');
    }
};
