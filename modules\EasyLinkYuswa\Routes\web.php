<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON><PERSON>\EasyLinkYuswa\Http\Controllers\DashboardController;
use Modules\EasyLinkYuswa\Http\Controllers\EasyLinkController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your EasyLinkYuswa module.
| These routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group.
|
*/

Route::prefix('easylinkyuswa')->name('easylinkyuswa.')->group(function () {
    
    // Dashboard Routes
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard.index');
    Route::get('/info', [DashboardController::class, 'info'])->name('info');
    Route::get('/settings', [DashboardController::class, 'settings'])->name('settings');

    // API Routes for AJAX calls
    Route::prefix('api')->name('api.')->group(function () {
        Route::get('/device-info', [EasyLinkController::class, 'getDeviceInfo'])->name('device.info');
        Route::get('/attendance-logs', [EasyLinkController::class, 'getAttendanceLogs'])->name('attendance.logs');
        Route::get('/check-connectivity', [EasyLinkController::class, 'checkConnectivity'])->name('connectivity.check');
        Route::get('/configuration', [EasyLinkController::class, 'getConfiguration'])->name('configuration');
        Route::get('/stats', [EasyLinkController::class, 'getStats'])->name('stats');
        Route::get('/device-status', [EasyLinkController::class, 'getDeviceStatus'])->name('device.status');
        Route::get('/dashboard-data', [DashboardController::class, 'getDashboardData'])->name('dashboard.data');
        Route::get('/realtime-status', [DashboardController::class, 'getRealtimeStatus'])->name('realtime.status');
        
        // Test Routes
        Route::get('/test-device', [EasyLinkController::class, 'testDevice'])->name('test.device');
        Route::get('/test-attendance', [EasyLinkController::class, 'testAttendance'])->name('test.attendance');
        Route::get('/test-module', [DashboardController::class, 'testModule'])->name('test.module');
        
        // Action Routes
        Route::post('/sync-attendance', [EasyLinkController::class, 'syncAttendance'])->name('sync.attendance');
        Route::post('/clear-cache', [DashboardController::class, 'clearCache'])->name('cache.clear');
        Route::get('/export-config', [DashboardController::class, 'exportConfig'])->name('config.export');
    });
});
