<?php

use Illuminate\Support\Facades\Route;
use Modules\EasylinkKangangga\Http\Controllers\EasylinkController;

/*
|--------------------------------------------------------------------------
| EasyLink Kangangga Module API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for the EasyLink Kangangga module.
| These routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group.
|
*/

// Device Management
Route::prefix('device')->name('device.')->group(function () {
    Route::get('/info', [EasylinkController::class, 'getDeviceInfo'])->name('info');
    Route::get('/connectivity', [EasylinkController::class, 'checkConnectivity'])->name('connectivity');
    Route::get('/test', [EasylinkController::class, 'testDevice'])->name('test');
});

// Attendance Management
Route::prefix('attendance')->name('attendance.')->group(function () {
    Route::get('/logs', [EasylinkController::class, 'getAttendanceLogs'])->name('logs');
    Route::get('/test', [EasylinkController::class, 'testAttendance'])->name('test');
});

// Configuration
Route::get('/configuration', [EasylinkController::class, 'getConfiguration'])->name('configuration');

// Health Check
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'module' => 'EasylinkKangangga',
        'timestamp' => now()->toISOString(),
        'version' => '1.0.0'
    ]);
})->name('health');
