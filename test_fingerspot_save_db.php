<?php

echo "🚀 Testing Fingerspot.io Get Attlog API with Database Save...\n\n";

// Test parameters
$testData = [
    'start_date' => '2025-06-12',
    'end_date' => '2025-06-14',
    'limit' => 10
];

echo "📋 Test Parameters:\n";
echo "   Start Date: {$testData['start_date']}\n";
echo "   End Date: {$testData['end_date']}\n";
echo "   Limit: {$testData['limit']}\n\n";

echo "🔄 Making API request to test-attlog-save endpoint...\n";

// Initialize cURL
$ch = curl_init();

curl_setopt_array($ch, [
    CURLOPT_URL => 'http://larattendance.me/fingerspot/test-attlog-save',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($testData),
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',
        'Accept: application/json',
    ],
    CURLOPT_TIMEOUT => 60,
    CURLOPT_CONNECTTIMEOUT => 10,
    CURLOPT_VERBOSE => false,
]);

$startTime = microtime(true);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
$executionTime = round((microtime(true) - $startTime) * 1000, 2);

curl_close($ch);

echo "⏱️  Execution time: {$executionTime}ms\n";
echo "📊 HTTP Status: {$httpCode}\n\n";

if ($error) {
    echo "❌ cURL Error: {$error}\n";
    exit(1);
}

if ($response === false) {
    echo "❌ Failed to get response from API\n";
    exit(1);
}

echo "🔍 Raw Response:\n";
echo $response . "\n\n";

// Parse response
$data = json_decode($response, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    echo "⚠️  Response is not valid JSON, showing raw response above\n";
} else {
    // Display results
    if ($httpCode >= 200 && $httpCode < 300) {
        echo "✅ API call successful!\n\n";
        
        if (isset($data['success']) && $data['success']) {
            echo "🎉 SUCCESS! Get attlog API with database save is working!\n\n";
            
            echo "📋 Database Save Summary:\n";
            $dbSummary = $data['database_summary'] ?? [];
            echo "   Total Fetched: " . ($dbSummary['total_fetched'] ?? 0) . "\n";
            echo "   Successfully Saved: " . ($dbSummary['saved_count'] ?? 0) . "\n";
            echo "   Errors: " . ($dbSummary['errors_count'] ?? 0) . "\n";
            
            if (!empty($dbSummary['errors'])) {
                echo "\n⚠️  Errors encountered:\n";
                foreach ($dbSummary['errors'] as $error) {
                    echo "   • {$error}\n";
                }
            }
            
            echo "\n📊 Recent Database Records:\n";
            $recentLogs = $data['recent_logs'] ?? [];
            if (!empty($recentLogs)) {
                foreach (array_slice($recentLogs, 0, 5) as $index => $log) {
                    echo "   Record " . ($index + 1) . ":\n";
                    echo "     ID: {$log['id']}\n";
                    echo "     Employee ID: {$log['employee_id']}\n";
                    echo "     Device ID: " . ($log['device_id'] ?? 'N/A') . "\n";
                    echo "     Timestamp: {$log['timestamp']}\n";
                    echo "     Status: {$log['sync_status']}\n";
                    echo "     Created: {$log['created_at']}\n\n";
                }
            } else {
                echo "   No recent records found.\n";
            }
            
        } else {
            echo "⚠️  API responded but returned error:\n";
            echo "   Error: " . ($data['message'] ?? 'Unknown error') . "\n";
        }
        
        echo "\n📋 Full Response:\n";
        echo json_encode($data, JSON_PRETTY_PRINT) . "\n";
        
    } else {
        echo "❌ API call failed!\n\n";
        echo "📋 Error Response:\n";
        echo json_encode($data, JSON_PRETTY_PRINT) . "\n";
    }
}

echo "\n✨ Test completed!\n";
