<?php

namespace Modules\Fingerspot\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Modules\Fingerspot\Services\FingerspotApiService;
use Exception;

class FingerspotApiController extends Controller
{
    public function __construct(
        private FingerspotApiService $apiService
    ) {}

    /**
     * Get device list
     */
    public function getDevices(Request $request): JsonResponse
    {
        try {
            $page = $request->get('page', 1);
            $limit = $request->get('limit', 50);
            
            $result = $this->apiService->getDevices($page, $limit);
            
            return response()->json([
                'success' => $result['success'],
                'data' => $result['data'] ?? null,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit
                ],
                'api_response' => $result
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get device information
     */
    public function getDevice(Request $request, $deviceId): JsonResponse
    {
        try {
            $result = $this->apiService->getDevice($deviceId);
            
            return response()->json([
                'success' => $result['success'],
                'data' => $result['data'] ?? null,
                'device_id' => $deviceId,
                'api_response' => $result
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get device status
     */
    public function getDeviceStatus(Request $request, $deviceId): JsonResponse
    {
        try {
            $result = $this->apiService->getDeviceStatus($deviceId);
            
            return response()->json([
                'success' => $result['success'],
                'data' => $result['data'] ?? null,
                'device_id' => $deviceId,
                'api_response' => $result
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get attendance logs
     */
    public function getAttendanceLogs(Request $request): JsonResponse
    {
        try {
            $deviceId = $request->get('device_id');
            $startDate = $request->get('start_date');
            $endDate = $request->get('end_date');
            $page = $request->get('page', 1);
            $limit = $request->get('limit', 100);
            
            $result = $this->apiService->getAttendanceLogs($deviceId, $startDate, $endDate, $page, $limit);
            
            return response()->json([
                'success' => $result['success'],
                'data' => $result['data'] ?? null,
                'filters' => [
                    'device_id' => $deviceId,
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'page' => $page,
                    'limit' => $limit
                ],
                'api_response' => $result
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get users/employees
     */
    public function getUsers(Request $request): JsonResponse
    {
        try {
            $deviceId = $request->get('device_id');
            $page = $request->get('page', 1);
            $limit = $request->get('limit', 100);
            
            $result = $this->apiService->getUsers($deviceId, $page, $limit);
            
            return response()->json([
                'success' => $result['success'],
                'data' => $result['data'] ?? null,
                'filters' => [
                    'device_id' => $deviceId,
                    'page' => $page,
                    'limit' => $limit
                ],
                'api_response' => $result
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create user
     */
    public function createUser(Request $request): JsonResponse
    {
        try {
            $userData = $request->validate([
                'name' => 'required|string|max:255',
                'employee_id' => 'required|string|max:100',
                'email' => 'nullable|email',
                'phone' => 'nullable|string|max:20',
                'department' => 'nullable|string|max:100',
                'position' => 'nullable|string|max:100',
            ]);
            
            $result = $this->apiService->createUser($userData);
            
            return response()->json([
                'success' => $result['success'],
                'data' => $result['data'] ?? null,
                'user_data' => $userData,
                'api_response' => $result
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update user
     */
    public function updateUser(Request $request, $userId): JsonResponse
    {
        try {
            $userData = $request->validate([
                'name' => 'sometimes|string|max:255',
                'employee_id' => 'sometimes|string|max:100',
                'email' => 'nullable|email',
                'phone' => 'nullable|string|max:20',
                'department' => 'nullable|string|max:100',
                'position' => 'nullable|string|max:100',
            ]);
            
            $result = $this->apiService->updateUser($userId, $userData);
            
            return response()->json([
                'success' => $result['success'],
                'data' => $result['data'] ?? null,
                'user_id' => $userId,
                'user_data' => $userData,
                'api_response' => $result
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete user
     */
    public function deleteUser(Request $request, $userId): JsonResponse
    {
        try {
            $result = $this->apiService->deleteUser($userId);
            
            return response()->json([
                'success' => $result['success'],
                'data' => $result['data'] ?? null,
                'user_id' => $userId,
                'api_response' => $result
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Sync user to device
     */
    public function syncUserToDevice(Request $request, $deviceId, $userId): JsonResponse
    {
        try {
            $result = $this->apiService->syncUserToDevice($deviceId, $userId);
            
            return response()->json([
                'success' => $result['success'],
                'data' => $result['data'] ?? null,
                'device_id' => $deviceId,
                'user_id' => $userId,
                'api_response' => $result
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send device command
     */
    public function sendDeviceCommand(Request $request, $deviceId): JsonResponse
    {
        try {
            $command = $request->input('command');
            $parameters = $request->input('parameters', []);
            
            $result = $this->apiService->sendDeviceCommand($deviceId, $command, $parameters);
            
            return response()->json([
                'success' => $result['success'],
                'data' => $result['data'] ?? null,
                'device_id' => $deviceId,
                'command' => $command,
                'parameters' => $parameters,
                'api_response' => $result
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test API connectivity
     */
    public function testConnectivity(): JsonResponse
    {
        try {
            $result = $this->apiService->testConnectivity();
            
            return response()->json([
                'success' => true,
                'connectivity' => $result,
                'timestamp' => now()->toDateTimeString()
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'connectivity' => [
                    'connected' => false,
                    'status' => 'error',
                    'error' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Get API configuration
     */
    public function getConfiguration(): JsonResponse
    {
        try {
            $config = $this->apiService->getConfiguration();
            
            return response()->json([
                'success' => true,
                'configuration' => $config,
                'timestamp' => now()->toDateTimeString()
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get API usage statistics
     */
    public function getApiUsage(Request $request): JsonResponse
    {
        try {
            $startDate = $request->get('start_date');
            $endDate = $request->get('end_date');
            
            $result = $this->apiService->getApiUsage($startDate, $endDate);
            
            return response()->json([
                'success' => $result['success'],
                'data' => $result['data'] ?? null,
                'filters' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ],
                'api_response' => $result
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear API cache
     */
    public function clearCache(): JsonResponse
    {
        try {
            $this->apiService->clearCache();
            
            return response()->json([
                'success' => true,
                'message' => 'API cache cleared successfully',
                'timestamp' => now()->toDateTimeString()
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
