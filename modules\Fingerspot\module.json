{"name": "Fingerspot", "alias": "fingerspot", "description": "Fingerspot Developer API and Webhook Integration Module", "keywords": ["fingerspot", "api", "webhook", "attendance", "biometric", "developer"], "priority": 0, "providers": ["Modules\\Fingerspot\\Providers\\FingerspotServiceProvider", "Modules\\Fingerspot\\Providers\\RouteServiceProvider"], "aliases": {}, "files": [], "requires": [], "version": "1.0.0", "dependencies": {"parent_composer": true, "required_packages": ["guzzlehttp/guzzle"]}}