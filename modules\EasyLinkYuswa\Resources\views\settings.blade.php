@extends('easylinkyuswa::layouts.app')

@section('title', 'EasyLink Yuswa Settings')

@section('content')
<div class="row">
    <!-- Page Header -->
    <div class="col-12 mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 text-gradient">
                    <i class="fas fa-cog me-2"></i>Module Settings
                </h1>
                <p class="text-muted">Configure your EasyLink Yuswa module settings</p>
            </div>
            <div>
                <a href="{{ route('easylinkyuswa.dashboard') }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Current Configuration -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Current Configuration
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Note:</strong> Configuration changes require updating your <code>.env</code> file and clearing the cache.
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6>Device Configuration</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>SDK Host:</strong></td>
                                <td><code>{{ $config['sdk_host'] }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>Serial Number:</strong></td>
                                <td><code>{{ $config['sdk_sn'] ?: 'Not configured' }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>Server Port:</strong></td>
                                <td><code>{{ $config['server_port'] }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>Timeout:</strong></td>
                                <td><code>{{ $config['timeout'] }}s</code></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Module Settings</h6>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Module Enabled:</strong></td>
                                <td>
                                    <span class="badge bg-{{ $config['enabled'] ? 'success' : 'danger' }}">
                                        {{ $config['enabled'] ? 'Yes' : 'No' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Debug Mode:</strong></td>
                                <td>
                                    <span class="badge bg-{{ $config['debug'] ? 'warning' : 'secondary' }}">
                                        {{ $config['debug'] ? 'On' : 'Off' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Auto Refresh:</strong></td>
                                <td>
                                    <span class="badge bg-{{ $config['auto_refresh'] ? 'success' : 'secondary' }}">
                                        {{ $config['auto_refresh'] ? 'On' : 'Off' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Refresh Interval:</strong></td>
                                <td><code>{{ $config['refresh_interval'] }}s</code></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>Configuration Tools
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="testConfiguration()">
                        <i class="fas fa-check-circle me-1"></i>Test Configuration
                    </button>
                    <button class="btn btn-success" onclick="exportConfig()">
                        <i class="fas fa-download me-1"></i>Export Config
                    </button>
                    <button class="btn btn-warning" onclick="clearCache()">
                        <i class="fas fa-broom me-1"></i>Clear Cache
                    </button>
                    <button class="btn btn-info" onclick="reloadConfig()">
                        <i class="fas fa-sync me-1"></i>Reload Config
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Environment Variables Guide -->
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-file-code me-2"></i>Environment Variables Guide
                </h6>
            </div>
            <div class="card-body">
                <p>Add these variables to your <code>.env</code> file to configure the EasyLink Yuswa module:</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>Device Configuration</h6>
                        <pre class="bg-light p-3 rounded"><code># EasyLink Device Configuration
EASYLINK_YUSWA_SDK_HOST=http://**************:5005
EASYLINK_YUSWA_SDK_SN=66208023321907
EASYLINK_YUSWA_SERVER_PORT=7005</code></pre>
                    </div>
                    <div class="col-md-6">
                        <h6>Database Configuration</h6>
                        <pre class="bg-light p-3 rounded"><code># Database Configuration
EASYLINK_YUSWA_DB_HOST=localhost
EASYLINK_YUSWA_DB_DATABASE=fingerspot
EASYLINK_YUSWA_DB_USERNAME=root
EASYLINK_YUSWA_DB_PASSWORD=</code></pre>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6>Module Settings</h6>
                        <pre class="bg-light p-3 rounded"><code># Module Settings
EASYLINK_YUSWA_MODULE_ENABLED=true
EASYLINK_YUSWA_DEBUG=false
EASYLINK_YUSWA_TIMEOUT=30</code></pre>
                    </div>
                    <div class="col-md-6">
                        <h6>Advanced Settings</h6>
                        <pre class="bg-light p-3 rounded"><code># Advanced Settings
EASYLINK_YUSWA_AUTO_REFRESH=true
EASYLINK_YUSWA_REFRESH_INTERVAL=30
EASYLINK_YUSWA_LOGGING_ENABLED=true</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Configuration Validation -->
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-shield-alt me-2"></i>Configuration Validation
                </h6>
            </div>
            <div class="card-body">
                <div id="validation-results">
                    <div class="text-center">
                        <button class="btn btn-primary" onclick="validateConfiguration()">
                            <i class="fas fa-check me-1"></i>Validate Configuration
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function testConfiguration() {
    $.get('{{ route("easylinkyuswa.api.test.module") }}')
        .done(function(response) {
            if (response.overall_test_result === 'passed') {
                showAlert('Configuration test passed! All settings are working correctly.', 'success');
            } else {
                showAlert('Configuration test failed. Please check your settings.', 'danger');
            }
        })
        .fail(function() {
            showAlert('Configuration test failed to execute', 'danger');
        });
}

function exportConfig() {
    $.get('{{ route("easylinkyuswa.api.config.export") }}')
        .done(function(response) {
            if (response.success) {
                const dataStr = JSON.stringify(response.data, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = response.filename;
                link.click();
                showAlert('Configuration exported successfully', 'success');
            } else {
                showAlert('Failed to export configuration', 'danger');
            }
        })
        .fail(function() {
            showAlert('Export failed', 'danger');
        });
}

function clearCache() {
    $.post('{{ route("easylinkyuswa.api.cache.clear") }}')
        .done(function(response) {
            if (response.success) {
                showAlert('Cache cleared successfully. Configuration reloaded.', 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('Failed to clear cache: ' + response.message, 'danger');
            }
        })
        .fail(function() {
            showAlert('Failed to clear cache', 'danger');
        });
}

function reloadConfig() {
    clearCache();
}

function validateConfiguration() {
    $('#validation-results').html('<div class="text-center"><div class="spinner-border" role="status"></div><p class="mt-2">Validating configuration...</p></div>');
    
    $.get('{{ route("easylinkyuswa.api.test.module") }}')
        .done(function(response) {
            let validationHtml = '<h6>Validation Results</h6>';
            
            // Overall result
            validationHtml += `<div class="alert alert-${response.overall_test_result === 'passed' ? 'success' : 'danger'}">
                <h6><i class="fas fa-${response.overall_test_result === 'passed' ? 'check' : 'times'} me-2"></i>Overall Result: ${response.overall_test_result.toUpperCase()}</h6>
            </div>`;
            
            // Individual tests
            validationHtml += '<div class="row">';
            
            // Connectivity
            const connectivityStatus = response.data.connectivity.connected ? 'success' : 'danger';
            validationHtml += `<div class="col-md-4 mb-3">
                <div class="card border-${connectivityStatus}">
                    <div class="card-body text-center">
                        <i class="fas fa-wifi fa-2x text-${connectivityStatus} mb-2"></i>
                        <h6>Connectivity</h6>
                        <span class="badge bg-${connectivityStatus}">${response.data.connectivity.connected ? 'PASS' : 'FAIL'}</span>
                    </div>
                </div>
            </div>`;
            
            // Device Info
            const deviceStatus = response.data.device_info_status === 'success' ? 'success' : 'danger';
            validationHtml += `<div class="col-md-4 mb-3">
                <div class="card border-${deviceStatus}">
                    <div class="card-body text-center">
                        <i class="fas fa-microchip fa-2x text-${deviceStatus} mb-2"></i>
                        <h6>Device Info</h6>
                        <span class="badge bg-${deviceStatus}">${response.data.device_info_status === 'success' ? 'PASS' : 'FAIL'}</span>
                    </div>
                </div>
            </div>`;
            
            // Attendance Logs
            const attendanceStatus = response.data.attendance_logs_status === 'success' ? 'success' : 'danger';
            validationHtml += `<div class="col-md-4 mb-3">
                <div class="card border-${attendanceStatus}">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x text-${attendanceStatus} mb-2"></i>
                        <h6>Attendance Logs</h6>
                        <span class="badge bg-${attendanceStatus}">${response.data.attendance_logs_status === 'success' ? 'PASS' : 'FAIL'}</span>
                    </div>
                </div>
            </div>`;
            
            validationHtml += '</div>';
            
            // Recommendations
            if (response.overall_test_result !== 'passed') {
                validationHtml += '<div class="alert alert-warning"><h6>Recommendations:</h6><ul class="mb-0">';
                if (!response.data.connectivity.connected) {
                    validationHtml += '<li>Check device IP address and network connectivity</li>';
                }
                if (response.data.device_info_status !== 'success') {
                    validationHtml += '<li>Verify device serial number and API endpoints</li>';
                }
                if (response.data.attendance_logs_status !== 'success') {
                    validationHtml += '<li>Ensure device has attendance data and proper permissions</li>';
                }
                validationHtml += '</ul></div>';
            }
            
            validationHtml += `<small class="text-muted">Validation completed at: ${formatDateTime(response.test_timestamp)}</small>`;
            
            $('#validation-results').html(validationHtml);
        })
        .fail(function() {
            $('#validation-results').html('<div class="alert alert-danger">Validation failed to execute</div>');
        });
}
</script>
@endsection
