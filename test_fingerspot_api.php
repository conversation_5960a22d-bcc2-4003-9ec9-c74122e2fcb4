<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Config;
use Modules\Fingerspot\Services\FingerspotApiService;

// Bootstrap Laravel
$app = new Application(realpath(__DIR__));
$app->singleton(
    Illuminate\Contracts\Http\Kernel::class,
    App\Http\Kernel::class
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);
$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);

// Load environment
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Bootstrap the application
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Fingerspot API Configuration Test ===\n\n";

// Test configuration loading
echo "1. Testing Configuration Loading:\n";
echo "   API Base URL: " . config('fingerspot.api.base_url') . "\n";
echo "   API Key: " . (config('fingerspot.auth.api_key') ? 'Configured' : 'Not configured') . "\n";
echo "   Cloud ID: " . config('fingerspot.fingerspot.cloud_id') . "\n";
echo "   Server: " . config('fingerspot.fingerspot.server') . "\n";
echo "   Server Port: " . config('fingerspot.fingerspot.server_port') . "\n\n";

// Test service instantiation
echo "2. Testing Service Instantiation:\n";
try {
    $apiService = new FingerspotApiService();
    echo "   ✓ Service instantiated successfully\n";
    
    $config = $apiService->getConfiguration();
    echo "   ✓ Configuration retrieved:\n";
    foreach ($config as $key => $value) {
        if (is_array($value)) {
            echo "     - $key: " . json_encode($value) . "\n";
        } else {
            echo "     - $key: $value\n";
        }
    }
} catch (Exception $e) {
    echo "   ✗ Error: " . $e->getMessage() . "\n";
}

echo "\n3. Testing API Connectivity:\n";
try {
    $connectivity = $apiService->testConnectivity();
    echo "   Status: " . $connectivity['status'] . "\n";
    echo "   Connected: " . ($connectivity['connected'] ? 'Yes' : 'No') . "\n";
    echo "   Response Time: " . $connectivity['response_time'] . "\n";
    echo "   Cloud ID: " . $connectivity['cloud_id'] . "\n";
    echo "   Server: " . $connectivity['server'] . "\n";
    echo "   Server Port: " . $connectivity['server_port'] . "\n";
    
    if (isset($connectivity['error'])) {
        echo "   Error: " . $connectivity['error'] . "\n";
    }
    
    if (isset($connectivity['api_response'])) {
        echo "   API Response: " . json_encode($connectivity['api_response'], JSON_PRETTY_PRINT) . "\n";
    }
} catch (Exception $e) {
    echo "   ✗ Connectivity test failed: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
