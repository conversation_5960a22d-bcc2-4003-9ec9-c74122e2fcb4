<?php

namespace Modules\EasylinkKangangga\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Illuminate\View\View;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;

class DashboardController extends Controller
{
    /**
     * Display the EasyLink SDK test dashboard
     */
    public function index(): View
    {
        $config = [
            'module_name' => config('easylinkangga.name'),
            'module_alias' => config('easylinkangga.alias'),
            'sdk_host' => config('easylinkangga.sdk_host'),
            'sdk_sn' => config('easylinkangga.sdk_sn'),
            'dashboard_title' => config('easylinkangga.dashboard.title'),
            'auto_refresh' => config('easylinkangga.dashboard.auto_refresh'),
            'refresh_interval' => config('easylinkangga.dashboard.refresh_interval'),
        ];

        return view('easylinkangga::dashboard', compact('config'));
    }

    /**
     * Display module information
     */
    public function info(): View
    {
        $moduleInfo = [
            'name' => config('easylinkangga.name'),
            'alias' => config('easylinkangga.alias'),
            'description' => config('easylinkangga.description'),
            'version' => '1.0.0',
            'author' => 'Laravel Attendance System',
            'package' => 'kangangga/laravel-easylink',
        ];

        return view('easylinkangga::info', compact('moduleInfo'));
    }

    /**
     * Display module settings
     */
    public function settings(): View
    {
        $settings = config('easylinkangga');

        // Ensure all required keys exist with defaults
        $settings = array_merge([
            'name' => 'EasylinkKangangga',
            'alias' => 'easylinkangga',
            'description' => 'EasyLink SDK Module by Kangangga',
            'sdk_host' => env('EASYLINK_SDK_HOST'),
            'sdk_sn' => env('EASYLINK_SDK_SN'),
            'server_port' => env('EASYLINK_SERVER_PORT', 7005),
            'db_host' => env('EASYLINK_DB_HOST', 'localhost'),
            'db_port' => env('EASYLINK_DB_PORT', 3306),
            'db_database' => env('EASYLINK_DB_DATABASE'),
            'db_username' => env('EASYLINK_DB_USERNAME'),
            'db_password' => env('EASYLINK_DB_PASSWORD'),
            'enabled' => env('EASYLINK_MODULE_ENABLED', true),
            'debug' => env('EASYLINK_DEBUG', false),
            'timeout' => env('EASYLINK_TIMEOUT', 30),
            'retry_attempts' => env('EASYLINK_RETRY_ATTEMPTS', 3),
            'dashboard' => [
                'title' => env('EASYLINK_DASHBOARD_TITLE', 'EasyLink SDK Dashboard'),
                'auto_refresh' => env('EASYLINK_AUTO_REFRESH', true),
                'refresh_interval' => env('EASYLINK_REFRESH_INTERVAL', 30),
                'theme' => env('EASYLINK_THEME', 'default'),
            ],
            'api' => [
                'rate_limit' => env('EASYLINK_API_RATE_LIMIT', 60),
                'cache_ttl' => env('EASYLINK_CACHE_TTL', 300),
                'response_format' => env('EASYLINK_RESPONSE_FORMAT', 'json'),
            ],
        ], $settings ?: []);

        return view('easylinkangga::settings', compact('settings'));
    }

    /**
     * Clear application cache
     */
    public function clearCache(): JsonResponse
    {
        try {
            // Clear various caches
            Artisan::call('config:clear');
            Artisan::call('cache:clear');
            Artisan::call('route:clear');
            Artisan::call('view:clear');

            // Clear specific cache tags if using tagged cache
            Cache::flush();

            return response()->json([
                'success' => true,
                'message' => 'Cache cleared successfully',
                'cleared' => [
                    'config' => 'Configuration cache cleared',
                    'application' => 'Application cache cleared',
                    'routes' => 'Route cache cleared',
                    'views' => 'View cache cleared',
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear cache: ' . $e->getMessage()
            ], 500);
        }
    }
}
