<?php

// Simple test script for Fingerspot.io get_attlog API

// Load environment variables manually
if (file_exists('.env')) {
    $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

echo "🚀 Testing Fingerspot.io Get Attlog API...\n\n";

// Configuration
$config = [
    'base_url' => $_ENV['FINGERSPOT_API_BASE_URL'] ?? 'https://developer.fingerspot.io/api',
    'cloud_id' => $_ENV['FINGERSPOT_CLOUDID'] ?? '',
    'server' => $_ENV['FINGERSPOT_SERVER'] ?? '',
    'server_port' => $_ENV['FINGERSPOT_SERVERPORT'] ?? '',
    'api_key' => $_ENV['FINGERSPOT_API_KEY'] ?? '',
];

echo "⚙️  Configuration:\n";
echo "   Base URL: {$config['base_url']}\n";
echo "   Cloud ID: {$config['cloud_id']}\n";
echo "   Server: {$config['server']}\n";
echo "   Server Port: {$config['server_port']}\n";
echo "   API Key: " . (empty($config['api_key']) ? '❌ Not set' : '✅ Set') . "\n\n";

// Test parameters
$startDate = date('Y-m-d', strtotime('-7 days'));
$endDate = date('Y-m-d');

echo "📋 Test Parameters:\n";
echo "   Start Date: {$startDate}\n";
echo "   End Date: {$endDate}\n";
echo "   Limit: 10\n\n";

// Test different parameter formats
$testCases = [
    'format_1' => [
        'cloudid' => $config['cloud_id'],
        'start_date' => $startDate,
        'end_date' => $endDate,
        'page' => 1,
        'limit' => 10
    ],
    'format_2' => [
        'cloud_id' => $config['cloud_id'],
        'start_date' => $startDate,
        'end_date' => $endDate,
        'page' => 1,
        'limit' => 10
    ],
    'format_3' => [
        'cloudid' => $config['cloud_id'],
        'startdate' => $startDate,
        'enddate' => $endDate,
        'page' => 1,
        'limit' => 10
    ],
    'format_4' => [
        'cloudid' => $config['cloud_id'],
        'apikey' => $config['api_key'],
        'start_date' => $startDate,
        'end_date' => $endDate,
        'page' => 1,
        'limit' => 10
    ]
];

foreach ($testCases as $testName => $requestData) {
    echo "🔄 Testing {$testName} with parameters: " . json_encode($requestData) . "\n";

    // Initialize cURL
    $ch = curl_init();

    curl_setopt_array($ch, [
        CURLOPT_URL => $config['base_url'] . '/get_attlog',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($requestData),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json',
        ],
        CURLOPT_TIMEOUT => 30,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_VERBOSE => false,
    ]);

$startTime = microtime(true);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
$executionTime = round((microtime(true) - $startTime) * 1000, 2);

curl_close($ch);

echo "⏱️  Execution time: {$executionTime}ms\n";
echo "📊 HTTP Status: {$httpCode}\n\n";

if ($error) {
    echo "❌ cURL Error: {$error}\n";
    exit(1);
}

if ($response === false) {
    echo "❌ Failed to get response from API\n";
    exit(1);
}

// Parse response
$data = json_decode($response, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    echo "❌ Invalid JSON response:\n";
    echo $response . "\n";
    exit(1);
}

// Display results
if ($httpCode >= 200 && $httpCode < 300) {
    echo "✅ API call successful!\n\n";
    
    echo "📋 Response Structure:\n";
    if (is_array($data)) {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                echo "   {$key}: [array with " . count($value) . " items]\n";
            } else {
                echo "   {$key}: " . (is_string($value) ? $value : json_encode($value)) . "\n";
            }
        }
    }
    
    echo "\n🔍 Raw Response:\n";
    echo json_encode($data, JSON_PRETTY_PRINT) . "\n";
    
} else {
    echo "❌ API call failed!\n\n";
    echo "📋 Error Response:\n";
    echo json_encode($data, JSON_PRETTY_PRINT) . "\n";
}

echo "\n✨ Test completed!\n";
