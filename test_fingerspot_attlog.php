<?php

// Simple test script for Fingerspot.io get_attlog API

// Load environment variables manually
if (file_exists('.env')) {
    $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

echo "🚀 Testing Fingerspot.io Get Attlog API...\n\n";

// Configuration
$config = [
    'base_url' => $_ENV['FINGERSPOT_API_BASE_URL'] ?? 'https://developer.fingerspot.io/api',
    'cloud_id' => $_ENV['FINGERSPOT_CLOUDID'] ?? '',
    'server' => $_ENV['FINGERSPOT_SERVER'] ?? '',
    'server_port' => $_ENV['FINGERSPOT_SERVERPORT'] ?? '',
    'api_key' => $_ENV['FINGERSPOT_API_KEY'] ?? '',
];

echo "⚙️  Configuration:\n";
echo "   Base URL: {$config['base_url']}\n";
echo "   Cloud ID: {$config['cloud_id']}\n";
echo "   Server: {$config['server']}\n";
echo "   Server Port: {$config['server_port']}\n";
echo "   API Key: " . (empty($config['api_key']) ? '❌ Not set' : '✅ Set') . "\n\n";

// Test parameters
$startDate = date('Y-m-d', strtotime('-2 days'));
$endDate = date('Y-m-d');

echo "📋 Test Parameters:\n";
echo "   Start Date: {$startDate}\n";
echo "   End Date: {$endDate}\n";
echo "   Limit: 10\n\n";

// Prepare request data using correct Fingerspot.io format
$requestData = [
    'trans_id' => '1',
    'cloud_id' => $config['cloud_id'],
    'start_date' => $startDate,
    'end_date' => $endDate
];

echo "🔄 Making API request to get_attlog endpoint...\n";
echo "📋 Request data: " . json_encode($requestData) . "\n\n";

// Initialize cURL
$ch = curl_init($config['base_url'] . '/get_attlog');

curl_setopt_array($ch, [
    CURLOPT_SSL_VERIFYHOST => 0,
    CURLOPT_SSL_VERIFYPEER => 0,
    CURLOPT_POST => 1,
    CURLOPT_POSTFIELDS => json_encode($requestData),
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $config['api_key']
    ],
    CURLOPT_RETURNTRANSFER => 1,
    CURLOPT_FOLLOWLOCATION => 1,
    CURLOPT_TIMEOUT => 30,
]);

$startTime = microtime(true);
$result = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
$executionTime = round((microtime(true) - $startTime) * 1000, 2);

curl_close($ch);

echo "⏱️  Execution time: {$executionTime}ms\n";
echo "📊 HTTP Status: {$httpCode}\n\n";

if ($error) {
    echo "❌ cURL Error: {$error}\n";
    exit(1);
}

if ($result === false) {
    echo "❌ Failed to get response from API\n";
    exit(1);
}

echo "🔍 Raw Response:\n";
echo $result . "\n\n";

// Parse response
$data = json_decode($result, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    echo "⚠️  Response is not valid JSON, showing raw response above\n";
} else {
    // Display results
    if ($httpCode >= 200 && $httpCode < 300) {
        echo "✅ API call successful!\n\n";

        if (isset($data['success']) && $data['success']) {
            echo "🎉 SUCCESS! Get attlog API is working!\n\n";

            echo "📋 Response Summary:\n";
            if (isset($data['data']) && is_array($data['data'])) {
                echo "   Records found: " . count($data['data']) . "\n";
                echo "   Date range: {$startDate} to {$endDate}\n\n";

                echo "📊 Sample Records:\n";
                foreach (array_slice($data['data'], 0, 5) as $index => $record) {
                    echo "   Record " . ($index + 1) . ":\n";
                    foreach ($record as $key => $value) {
                        echo "     {$key}: {$value}\n";
                    }
                    echo "\n";
                }
            }
        } else {
            echo "⚠️  API responded but returned error:\n";
            echo "   Error: " . ($data['message'] ?? 'Unknown error') . "\n";
            echo "   Error Code: " . ($data['error_code'] ?? 'N/A') . "\n";
        }

        echo "\n� Full Response:\n";
        echo json_encode($data, JSON_PRETTY_PRINT) . "\n";

    } else {
        echo "❌ API call failed!\n\n";
        echo "📋 Error Response:\n";
        echo json_encode($data, JSON_PRETTY_PRINT) . "\n";
    }
}

echo "\n✨ Test completed!\n";
