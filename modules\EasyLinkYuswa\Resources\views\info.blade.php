@extends('easylinkyuswa::layouts.app')

@section('title', 'EasyLink Yuswa Module Info')

@section('content')
<div class="row">
    <!-- Page Header -->
    <div class="col-12 mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 text-gradient">
                    <i class="fas fa-info-circle me-2"></i>Module Information
                </h1>
                <p class="text-muted">Detailed information about the EasyLink Yuswa module</p>
            </div>
            <div>
                <a href="{{ route('easylinkyuswa.dashboard') }}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Module Details -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-cube me-2"></i>Module Details
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>Name:</strong></td>
                        <td>{{ $config['module_name'] }}</td>
                    </tr>
                    <tr>
                        <td><strong>Alias:</strong></td>
                        <td>{{ $config['module_alias'] }}</td>
                    </tr>
                    <tr>
                        <td><strong>Description:</strong></td>
                        <td>{{ $config['description'] }}</td>
                    </tr>
                    <tr>
                        <td><strong>Version:</strong></td>
                        <td>1.0.0</td>
                    </tr>
                    <tr>
                        <td><strong>Author:</strong></td>
                        <td>Laravel Attendance System</td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>
                            <span class="badge bg-{{ $config['enabled'] ? 'success' : 'danger' }}">
                                {{ $config['enabled'] ? 'Enabled' : 'Disabled' }}
                            </span>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Module Statistics
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>Module Enabled:</strong></td>
                        <td>
                            <span class="badge bg-{{ $stats['module_enabled'] ? 'success' : 'danger' }}">
                                {{ $stats['module_enabled'] ? 'Yes' : 'No' }}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Device Connected:</strong></td>
                        <td>
                            <span class="badge bg-{{ $stats['device_connected'] ? 'success' : 'danger' }}">
                                {{ $stats['device_connected'] ? 'Yes' : 'No' }}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Total Logs:</strong></td>
                        <td>{{ $stats['total_logs'] }}</td>
                    </tr>
                    <tr>
                        <td><strong>Errors Count:</strong></td>
                        <td>{{ $stats['errors_count'] }}</td>
                    </tr>
                    <tr>
                        <td><strong>Last Sync:</strong></td>
                        <td>{{ $stats['last_sync'] ?? 'Never' }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Configuration -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-cogs me-2"></i>Configuration
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Device Settings</h6>
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td><strong>SDK Host:</strong></td>
                                <td>{{ $config['sdk_host'] }}</td>
                            </tr>
                            <tr>
                                <td><strong>Serial Number:</strong></td>
                                <td>{{ $config['sdk_sn'] ?: 'Not configured' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Server Port:</strong></td>
                                <td>{{ $config['server_port'] }}</td>
                            </tr>
                            <tr>
                                <td><strong>Timeout:</strong></td>
                                <td>{{ $config['timeout'] }}s</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Module Settings</h6>
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td><strong>Debug Mode:</strong></td>
                                <td>{{ $config['debug'] ? 'On' : 'Off' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Auto Refresh:</strong></td>
                                <td>{{ $config['auto_refresh'] ? 'On' : 'Off' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Refresh Interval:</strong></td>
                                <td>{{ $config['refresh_interval'] }}s</td>
                            </tr>
                            <tr>
                                <td><strong>API Prefix:</strong></td>
                                <td>{{ $config['api_prefix'] }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="testModule()">
                        <i class="fas fa-vial me-1"></i>Test Module
                    </button>
                    <button class="btn btn-success" onclick="exportConfig()">
                        <i class="fas fa-download me-1"></i>Export Config
                    </button>
                    <button class="btn btn-warning" onclick="clearCache()">
                        <i class="fas fa-broom me-1"></i>Clear Cache
                    </button>
                    <a href="{{ route('easylinkyuswa.settings') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-cog me-1"></i>Settings
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Routes Information -->
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-route me-2"></i>Available Routes
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Web Routes</h6>
                        <ul class="list-unstyled">
                            <li><code>/easylinkyuswa</code> - Dashboard</li>
                            <li><code>/easylinkyuswa/info</code> - Module Info</li>
                            <li><code>/easylinkyuswa/settings</code> - Settings</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>API Routes</h6>
                        <ul class="list-unstyled">
                            <li><code>/api/easylinkyuswa/device/info</code> - Device Info</li>
                            <li><code>/api/easylinkyuswa/attendance/logs</code> - Attendance Logs</li>
                            <li><code>/api/easylinkyuswa/health</code> - Health Check</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function testModule() {
    $.get('{{ route("easylinkyuswa.api.test.module") }}')
        .done(function(response) {
            if (response.overall_test_result === 'passed') {
                showAlert('All module tests passed successfully!', 'success');
            } else {
                showAlert('Some module tests failed. Check the dashboard for details.', 'warning');
            }
        })
        .fail(function() {
            showAlert('Module test failed to execute', 'danger');
        });
}

function exportConfig() {
    $.get('{{ route("easylinkyuswa.api.config.export") }}')
        .done(function(response) {
            if (response.success) {
                const dataStr = JSON.stringify(response.data, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = response.filename;
                link.click();
                showAlert('Configuration exported successfully', 'success');
            } else {
                showAlert('Failed to export configuration', 'danger');
            }
        })
        .fail(function() {
            showAlert('Export failed', 'danger');
        });
}

function clearCache() {
    $.post('{{ route("easylinkyuswa.api.cache.clear") }}')
        .done(function(response) {
            if (response.success) {
                showAlert('Cache cleared successfully', 'success');
            } else {
                showAlert('Failed to clear cache: ' + response.message, 'danger');
            }
        })
        .fail(function() {
            showAlert('Failed to clear cache', 'danger');
        });
}
</script>
@endsection
