<?php

namespace Modules\EasylinkKangangga\Console;

use Illuminate\Console\Command;
use <PERSON><PERSON><PERSON>\EasylinkKangangga\Services\AttendanceService;

class TestEasylinkCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'easylinkangga:test {--config : Show configuration only}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test EasyLink Kangangga module configuration and connectivity';

    /**
     * Execute the console command.
     */
    public function handle(AttendanceService $attendanceService)
    {
        $this->info('Testing EasyLink Kangangga Module...');
        $this->newLine();
        
        // Display module information
        $this->info('Module Information:');
        $this->line('  Name: ' . config('easylinkangga.name'));
        $this->line('  Alias: ' . config('easylinkangga.alias'));
        $this->line('  Description: ' . config('easylinkangga.description'));
        $this->line('  Enabled: ' . (config('easylinkangga.enabled') ? 'Yes' : 'No'));
        
        $this->newLine();
        
        // Display current configuration
        $this->info('Configuration:');
        $config = $attendanceService->getConfiguration();
        foreach ($config as $key => $value) {
            $this->line('  ' . ucfirst(str_replace('_', ' ', $key)) . ': ' . ($value ?: 'Not set'));
        }
        
        if ($this->option('config')) {
            return;
        }
        
        $this->newLine();
        
        // Test device connectivity
        $this->info('Testing device connectivity...');
        try {
            $connectivity = $attendanceService->checkDeviceConnectivity();
            
            if ($connectivity['connected']) {
                $this->info('✓ Device is connected and responding');
                $this->line('Message: ' . $connectivity['message']);
            } else {
                $this->error('✗ Device connection failed');
                $this->line('Error: ' . $connectivity['message']);
            }
        } catch (\Exception $e) {
            $this->error('✗ Connection test failed: ' . $e->getMessage());
        }
        
        $this->newLine();
        
        // Test device info
        $this->info('Testing device info retrieval...');
        try {
            $deviceInfo = $attendanceService->getDeviceInfo();
            $this->info('✓ Device info retrieved successfully');
            $this->line('Response: ' . json_encode($deviceInfo->toArray(), JSON_PRETTY_PRINT));
        } catch (\Exception $e) {
            $this->error('✗ Failed to get device info: ' . $e->getMessage());
        }
        
        $this->newLine();
        
        // Test attendance logs
        $this->info('Testing attendance logs...');
        try {
            $logs = $attendanceService->getFormattedAttendanceLogs();
            
            if ($logs['success']) {
                $this->info('✓ Attendance logs retrieved successfully');
                $this->line('Count: ' . ($logs['count'] ?? 0) . ' records');
                
                if (!empty($logs['data'])) {
                    $this->line('Sample record: ' . json_encode($logs['data'][0] ?? [], JSON_PRETTY_PRINT));
                }
            } else {
                $this->warn('⚠ No attendance data available');
                $this->line('Message: ' . $logs['message']);
            }
        } catch (\Exception $e) {
            $this->error('✗ Failed to get attendance logs: ' . $e->getMessage());
        }
        
        $this->newLine();
        
        // Module statistics
        $this->info('Module Statistics:');
        $stats = $attendanceService->getModuleStats();
        foreach ($stats as $key => $value) {
            $label = ucfirst(str_replace('_', ' ', $key));
            $displayValue = is_bool($value) ? ($value ? 'Yes' : 'No') : $value;
            $this->line('  ' . $label . ': ' . $displayValue);
        }
        
        $this->newLine();
        $this->info('EasyLink Kangangga module test completed!');
        
        // Show helpful URLs
        $this->newLine();
        $this->info('Helpful URLs:');
        $this->line('  Dashboard: ' . url('/easylinkangga'));
        $this->line('  Module Info: ' . url('/easylinkangga/info'));
        $this->line('  API Health: ' . url('/api/easylinkangga/health'));
    }
}
