<?php

namespace Modules\Fingerspot\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Event;
use Modules\Fingerspot\Models\WebhookLog;
use Modules\Fingerspot\Models\AttendanceLog;
use Modules\Fingerspot\Models\DeviceLog;
use Modules\Fingerspot\Events\WebhookReceived;
use Modules\Fingerspot\Events\AttendanceReceived;
use Modules\Fingerspot\Events\DeviceStatusChanged;
use Exception;

class FingerspotWebhookService
{
    protected $webhookSecret;
    protected $verifySignature;
    protected $enabledEvents;

    public function __construct()
    {
        $this->webhookSecret = config('fingerspot.webhook.secret');
        $this->verifySignature = config('fingerspot.webhook.verify_signature', true);
        $this->enabledEvents = config('fingerspot.webhook.events', []);
    }

    /**
     * Process incoming webhook
     */
    public function processWebhook(Request $request, string $type = 'general')
    {
        try {
            // Verify webhook signature if enabled
            if ($this->verifySignature && !$this->verifyWebhookSignature($request)) {
                $this->logWebhook($request, $type, 'signature_verification_failed');
                return [
                    'success' => false,
                    'error' => 'Invalid webhook signature',
                    'status_code' => 401
                ];
            }

            // Get webhook payload
            $payload = $request->all();
            $headers = $request->headers->all();

            // Log webhook if enabled
            if (config('fingerspot.logging.webhook_logs')) {
                $this->logWebhook($request, $type, 'received', $payload);
            }

            // Process based on webhook type
            $result = $this->processWebhookByType($type, $payload, $headers);

            // Fire webhook received event
            Event::dispatch(new WebhookReceived($type, $payload, $headers));

            return [
                'success' => true,
                'message' => 'Webhook processed successfully',
                'type' => $type,
                'processed_at' => now()->toDateTimeString(),
                'result' => $result
            ];

        } catch (Exception $e) {
            $this->logError("Webhook processing failed for type: {$type}", $e);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'type' => $type,
                'status_code' => 500
            ];
        }
    }

    /**
     * Verify webhook signature
     */
    protected function verifyWebhookSignature(Request $request): bool
    {
        $signature = $request->header('X-Fingerspot-Signature');
        
        if (!$signature) {
            return false;
        }

        $payload = $request->getContent();
        $expectedSignature = hash_hmac('sha256', $payload, $this->webhookSecret);

        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Process webhook by type
     */
    protected function processWebhookByType(string $type, array $payload, array $headers)
    {
        switch ($type) {
            case 'attendance':
                return $this->processAttendanceWebhook($payload, $headers);
            
            case 'employee':
                return $this->processEmployeeWebhook($payload, $headers);
            
            case 'device':
                return $this->processDeviceWebhook($payload, $headers);
            
            case 'general':
            default:
                return $this->processGeneralWebhook($payload, $headers);
        }
    }

    /**
     * Process attendance webhook
     */
    protected function processAttendanceWebhook(array $payload, array $headers)
    {
        $eventType = $payload['event_type'] ?? 'attendance.created';
        
        if (!in_array($eventType, $this->enabledEvents)) {
            return ['skipped' => true, 'reason' => 'Event type not enabled'];
        }

        // Store attendance data if configured
        if (config('fingerspot.database.store_raw_data')) {
            $attendanceLog = AttendanceLog::create([
                'device_id' => $payload['device_id'] ?? null,
                'user_id' => $payload['user_id'] ?? null,
                'employee_id' => $payload['employee_id'] ?? null,
                'timestamp' => $payload['timestamp'] ?? now(),
                'event_type' => $eventType,
                'raw_data' => json_encode($payload),
                'processed_at' => now(),
            ]);

            // Fire attendance received event
            Event::dispatch(new AttendanceReceived($attendanceLog, $payload));

            return [
                'attendance_log_id' => $attendanceLog->id,
                'event_type' => $eventType,
                'processed' => true
            ];
        }

        return [
            'event_type' => $eventType,
            'processed' => true,
            'stored' => false
        ];
    }

    /**
     * Process employee webhook
     */
    protected function processEmployeeWebhook(array $payload, array $headers)
    {
        $eventType = $payload['event_type'] ?? 'employee.updated';
        
        if (!in_array($eventType, $this->enabledEvents)) {
            return ['skipped' => true, 'reason' => 'Event type not enabled'];
        }

        // Process employee data
        $employeeData = $payload['employee'] ?? $payload;
        
        return [
            'event_type' => $eventType,
            'employee_id' => $employeeData['id'] ?? null,
            'processed' => true
        ];
    }

    /**
     * Process device webhook
     */
    protected function processDeviceWebhook(array $payload, array $headers)
    {
        $eventType = $payload['event_type'] ?? 'device.status';
        
        if (!in_array($eventType, $this->enabledEvents)) {
            return ['skipped' => true, 'reason' => 'Event type not enabled'];
        }

        // Store device log if configured
        if (config('fingerspot.database.store_raw_data')) {
            $deviceLog = DeviceLog::create([
                'device_id' => $payload['device_id'] ?? null,
                'event_type' => $eventType,
                'status' => $payload['status'] ?? 'unknown',
                'message' => $payload['message'] ?? null,
                'raw_data' => json_encode($payload),
                'timestamp' => $payload['timestamp'] ?? now(),
                'processed_at' => now(),
            ]);

            // Fire device status changed event
            Event::dispatch(new DeviceStatusChanged($deviceLog, $payload));

            return [
                'device_log_id' => $deviceLog->id,
                'event_type' => $eventType,
                'processed' => true
            ];
        }

        return [
            'event_type' => $eventType,
            'device_id' => $payload['device_id'] ?? null,
            'processed' => true,
            'stored' => false
        ];
    }

    /**
     * Process general webhook
     */
    protected function processGeneralWebhook(array $payload, array $headers)
    {
        $eventType = $payload['event_type'] ?? 'general.notification';
        
        return [
            'event_type' => $eventType,
            'processed' => true,
            'payload_keys' => array_keys($payload)
        ];
    }

    /**
     * Log webhook data
     */
    protected function logWebhook(Request $request, string $type, string $status, array $payload = null)
    {
        try {
            WebhookLog::create([
                'type' => $type,
                'status' => $status,
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'headers' => json_encode($request->headers->all()),
                'payload' => $payload ? json_encode($payload) : $request->getContent(),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'received_at' => now(),
            ]);
        } catch (Exception $e) {
            $this->logError("Failed to log webhook", $e);
        }
    }

    /**
     * Get webhook statistics
     */
    public function getWebhookStats($days = 7)
    {
        $startDate = now()->subDays($days);
        
        return [
            'total_webhooks' => WebhookLog::where('received_at', '>=', $startDate)->count(),
            'successful_webhooks' => WebhookLog::where('received_at', '>=', $startDate)
                                               ->where('status', 'received')->count(),
            'failed_webhooks' => WebhookLog::where('received_at', '>=', $startDate)
                                          ->where('status', '!=', 'received')->count(),
            'by_type' => WebhookLog::where('received_at', '>=', $startDate)
                                  ->groupBy('type')
                                  ->selectRaw('type, count(*) as count')
                                  ->pluck('count', 'type'),
            'recent_webhooks' => WebhookLog::where('received_at', '>=', $startDate)
                                          ->orderBy('received_at', 'desc')
                                          ->limit(10)
                                          ->get(),
        ];
    }

    /**
     * Get webhook configuration
     */
    public function getWebhookConfiguration()
    {
        return [
            'enabled' => config('fingerspot.webhook.enabled'),
            'verify_signature' => $this->verifySignature,
            'secret_configured' => !empty($this->webhookSecret),
            'enabled_events' => $this->enabledEvents,
            'endpoints' => config('fingerspot.webhook.endpoints'),
            'logging_enabled' => config('fingerspot.logging.webhook_logs'),
            'store_raw_data' => config('fingerspot.database.store_raw_data'),
        ];
    }

    /**
     * Test webhook endpoint
     */
    public function testWebhookEndpoint(string $type = 'general')
    {
        $testPayload = [
            'event_type' => 'test.webhook',
            'timestamp' => now()->toDateTimeString(),
            'test_data' => [
                'message' => 'This is a test webhook',
                'type' => $type,
                'generated_by' => 'Fingerspot Module'
            ]
        ];

        return $this->processWebhookByType($type, $testPayload, []);
    }

    /**
     * Log error messages
     */
    protected function logError($message, Exception $exception)
    {
        if (config('fingerspot.logging.enabled')) {
            Log::channel(config('fingerspot.logging.channel', 'single'))
               ->error("[FingerspotWebhook] {$message}", [
                   'exception' => $exception->getMessage(),
                   'trace' => $exception->getTraceAsString(),
               ]);
        }
    }
}
