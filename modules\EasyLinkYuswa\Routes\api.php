<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\EasyLinkYuswa\Http\Controllers\EasyLinkController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your EasyLinkYuswa module.
| These routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group.
|
*/

Route::prefix('easylinkyuswa')->name('easylinkyuswa.api.')->middleware('api')->group(function () {
    
    // Device Management
    Route::prefix('device')->name('device.')->group(function () {
        Route::get('/info', [EasyLinkController::class, 'getDeviceInfo'])->name('info');
        Route::get('/connectivity', [EasyLinkController::class, 'checkConnectivity'])->name('connectivity');
        Route::get('/status', [EasyLinkController::class, 'getDeviceStatus'])->name('status');
        Route::get('/test', [EasyLinkController::class, 'testDevice'])->name('test');
    });

    // Attendance Management
    Route::prefix('attendance')->name('attendance.')->group(function () {
        Route::get('/logs', [EasyLinkController::class, 'getAttendanceLogs'])->name('logs');
        Route::get('/test', [EasyLinkController::class, 'testAttendance'])->name('test');
        Route::post('/sync', [EasyLinkController::class, 'syncAttendance'])->name('sync');
    });

    // Configuration Management
    Route::prefix('config')->name('config.')->group(function () {
        Route::get('/', [EasyLinkController::class, 'getConfiguration'])->name('get');
        Route::get('/stats', [EasyLinkController::class, 'getStats'])->name('stats');
    });

    // Health Check
    Route::get('/health', function () {
        return response()->json([
            'success' => true,
            'module' => 'EasyLinkYuswa',
            'status' => 'healthy',
            'timestamp' => now()->toDateTimeString(),
            'version' => '1.0.0'
        ]);
    })->name('health');

    // Module Information
    Route::get('/info', function () {
        return response()->json([
            'success' => true,
            'module' => [
                'name' => 'EasyLinkYuswa',
                'alias' => 'easylinkyuswa',
                'description' => 'EasyLink SDK Module by Yuswa Arba - Fingerspot EasyLink Device Integration',
                'version' => '1.0.0',
                'author' => 'Laravel Attendance System',
                'routes' => [
                    'web_prefix' => 'easylinkyuswa',
                    'api_prefix' => 'api/easylinkyuswa',
                    'dashboard' => url('/easylinkyuswa'),
                ]
            ],
            'timestamp' => now()->toDateTimeString()
        ]);
    })->name('info');
});
