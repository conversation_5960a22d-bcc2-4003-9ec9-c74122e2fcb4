<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>@yield('title', 'Fingerspot Module') - {{ config('app.name', 'Laravel') }}</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --fingerspot-primary: #2563eb;
            --fingerspot-secondary: #64748b;
            --fingerspot-success: #059669;
            --fingerspot-warning: #d97706;
            --fingerspot-danger: #dc2626;
            --fingerspot-info: #0891b2;
        }
        
        .navbar-brand {
            font-weight: 600;
            color: var(--fingerspot-primary) !important;
        }
        
        .card {
            border: none;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        
        .btn-fingerspot {
            background-color: var(--fingerspot-primary);
            border-color: var(--fingerspot-primary);
            color: white;
        }
        
        .btn-fingerspot:hover {
            background-color: #1d4ed8;
            border-color: #1d4ed8;
            color: white;
        }
        
        .status-online {
            color: var(--fingerspot-success);
        }
        
        .status-offline {
            color: var(--fingerspot-danger);
        }
        
        .status-warning {
            color: var(--fingerspot-warning);
        }
        
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: #f8fafc;
            border-right: 1px solid #e2e8f0;
        }
        
        .sidebar .nav-link {
            color: var(--fingerspot-secondary);
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin-bottom: 0.25rem;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: var(--fingerspot-primary);
            color: white;
        }
        
        .sidebar .nav-link i {
            width: 20px;
            margin-right: 0.5rem;
        }
        
        .main-content {
            padding: 2rem;
        }
        
        .stats-card {
            background: linear-gradient(135deg, var(--fingerspot-primary) 0%, #3b82f6 100%);
            color: white;
        }
        
        .stats-card .card-body {
            padding: 1.5rem;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .stats-label {
            font-size: 0.875rem;
            opacity: 0.9;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid #f3f4f6;
            border-radius: 50%;
            border-top-color: var(--fingerspot-primary);
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .alert-modern {
            border: none;
            border-radius: 0.5rem;
            padding: 1rem 1.25rem;
        }
        
        .table-modern {
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }
        
        .table-modern thead th {
            background-color: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            font-weight: 600;
            color: var(--fingerspot-secondary);
        }
    </style>
    
    @stack('styles')
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ route('fingerspot.dashboard') }}">
                <i class="fas fa-fingerprint me-2"></i>
                Fingerspot Module
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-cog"></i>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{{ route('fingerspot.settings') }}">
                            <i class="fas fa-cog me-2"></i>Settings
                        </a></li>
                        <li><a class="dropdown-item" href="{{ route('fingerspot.info') }}">
                            <i class="fas fa-info-circle me-2"></i>Module Info
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="clearCache()">
                            <i class="fas fa-trash me-2"></i>Clear Cache
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('fingerspot.dashboard*') ? 'active' : '' }}" 
                               href="{{ route('fingerspot.dashboard') }}">
                                <i class="fas fa-tachometer-alt"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('fingerspot.devices*') ? 'active' : '' }}" 
                               href="{{ route('fingerspot.devices') }}">
                                <i class="fas fa-desktop"></i>
                                Devices
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('fingerspot.attendance*') ? 'active' : '' }}" 
                               href="{{ route('fingerspot.attendance') }}">
                                <i class="fas fa-clock"></i>
                                Attendance
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('fingerspot.webhooks*') ? 'active' : '' }}" 
                               href="{{ route('fingerspot.webhooks') }}">
                                <i class="fas fa-webhook"></i>
                                Webhooks
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('fingerspot.info*') ? 'active' : '' }}" 
                               href="{{ route('fingerspot.info') }}">
                                <i class="fas fa-info-circle"></i>
                                Module Info
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('fingerspot.settings*') ? 'active' : '' }}" 
                               href="{{ route('fingerspot.settings') }}">
                                <i class="fas fa-cog"></i>
                                Settings
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                @yield('content')
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Common JavaScript -->
    <script>
        // CSRF token setup for AJAX
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        
        // Clear cache function
        function clearCache() {
            if (confirm('Are you sure you want to clear the cache?')) {
                $.post('{{ route("fingerspot.api.cache.clear") }}')
                    .done(function(response) {
                        if (response.success) {
                            alert('Cache cleared successfully!');
                            location.reload();
                        } else {
                            alert('Failed to clear cache: ' + response.message);
                        }
                    })
                    .fail(function() {
                        alert('Failed to clear cache. Please try again.');
                    });
            }
        }
        
        // Auto-refresh functionality
        let autoRefreshInterval;
        
        function startAutoRefresh(callback, interval = 30000) {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
            
            autoRefreshInterval = setInterval(callback, interval);
        }
        
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
        }
        
        // Format timestamp
        function formatTimestamp(timestamp) {
            return new Date(timestamp).toLocaleString();
        }
        
        // Show loading state
        function showLoading(element) {
            $(element).html('<span class="loading-spinner"></span> Loading...');
        }
        
        // Show error state
        function showError(element, message) {
            $(element).html('<span class="text-danger"><i class="fas fa-exclamation-triangle"></i> ' + message + '</span>');
        }
    </script>
    
    @stack('scripts')
</body>
</html>
