<?php

namespace Modules\Fingerspot\Console\Commands;

use Illuminate\Console\Command;
use Mo<PERSON>les\Fingerspot\Services\FingerspotApiService;
use Exception;

class TestFingerspotAttlog extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fingerspot:test-attlog 
                            {--device-id= : Specific device ID to test}
                            {--start-date= : Start date (Y-m-d format)}
                            {--end-date= : End date (Y-m-d format)}
                            {--limit=10 : Number of records to fetch}
                            {--verbose : Show detailed output}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Fingerspot.io get attlog API functionality';

    /**
     * Create a new command instance.
     */
    public function __construct(
        private FingerspotApiService $apiService
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Testing Fingerspot.io Get Attlog API...');
        $this->newLine();

        try {
            // Get options
            $deviceId = $this->option('device-id');
            $startDate = $this->option('start-date') ?: now()->subDays(7)->format('Y-m-d');
            $endDate = $this->option('end-date') ?: now()->format('Y-m-d');
            $limit = (int) $this->option('limit');
            $verbose = $this->option('verbose');

            // Display configuration
            $this->displayConfiguration($verbose);

            // Test parameters
            $this->info('📋 Test Parameters:');
            $this->table(
                ['Parameter', 'Value'],
                [
                    ['Device ID', $deviceId ?: 'All devices'],
                    ['Start Date', $startDate],
                    ['End Date', $endDate],
                    ['Limit', $limit],
                ]
            );
            $this->newLine();

            // Execute test
            $this->info('🔄 Executing get attlog API call...');
            $startTime = microtime(true);

            $result = $this->apiService->getAttendanceLogs($deviceId, $startDate, $endDate, 1, $limit);

            $executionTime = round((microtime(true) - $startTime) * 1000, 2);

            // Display results
            $this->displayResults($result, $executionTime, $verbose);

            return $result['success'] ? Command::SUCCESS : Command::FAILURE;

        } catch (Exception $e) {
            $this->error('❌ Test failed with exception:');
            $this->error($e->getMessage());
            
            if ($this->option('verbose')) {
                $this->newLine();
                $this->error('Stack trace:');
                $this->line($e->getTraceAsString());
            }

            return Command::FAILURE;
        }
    }

    /**
     * Display configuration information
     */
    private function displayConfiguration(bool $verbose): void
    {
        $this->info('⚙️  Configuration:');
        
        $config = $this->apiService->getConfiguration();
        
        $configTable = [
            ['API Base URL', $config['api_base_url']],
            ['Cloud ID', $config['cloud_id']],
            ['Server', $config['server']],
            ['API Key Configured', $config['api_key_configured'] ? '✅ Yes' : '❌ No'],
            ['Module Enabled', $config['features']['real_time_sync'] ? '✅ Yes' : '❌ No'],
        ];

        if ($verbose) {
            $configTable = array_merge($configTable, [
                ['Timeout', $config['timeout'] . 's'],
                ['Retry Attempts', $config['retry_attempts']],
                ['Debug Mode', $config['debug_mode'] ? '✅ Yes' : '❌ No'],
                ['Cache Enabled', $config['cache_enabled'] ? '✅ Yes' : '❌ No'],
            ]);
        }

        $this->table(['Setting', 'Value'], $configTable);
        $this->newLine();
    }

    /**
     * Display test results
     */
    private function displayResults(array $result, float $executionTime, bool $verbose): void
    {
        if ($result['success']) {
            $this->info('✅ API call successful!');
            $this->info("⏱️  Execution time: {$executionTime}ms");
            $this->newLine();

            // Display data summary
            $data = $result['data'] ?? [];
            $recordCount = is_array($data) ? count($data) : 0;

            $this->info('📊 Results Summary:');
            $this->table(
                ['Metric', 'Value'],
                [
                    ['Records Found', $recordCount],
                    ['HTTP Status', $result['http_status'] ?? 'N/A'],
                    ['Response Time', $executionTime . 'ms'],
                ]
            );

            // Display attendance records if any
            if ($recordCount > 0) {
                $this->newLine();
                $this->info('📋 Attendance Records:');
                
                $headers = ['#', 'Employee ID', 'Date/Time', 'Device', 'Type'];
                $rows = [];

                foreach (array_slice($data, 0, 10) as $index => $record) {
                    $rows[] = [
                        $index + 1,
                        $record['employee_id'] ?? $record['user_id'] ?? 'N/A',
                        $record['datetime'] ?? $record['timestamp'] ?? 'N/A',
                        $record['device_id'] ?? $record['device'] ?? 'N/A',
                        $record['type'] ?? $record['status'] ?? 'N/A',
                    ];
                }

                $this->table($headers, $rows);

                if ($recordCount > 10) {
                    $this->info("... and " . ($recordCount - 10) . " more records");
                }
            } else {
                $this->warn('⚠️  No attendance records found for the specified criteria.');
            }

            // Show raw response if verbose
            if ($verbose) {
                $this->newLine();
                $this->info('🔍 Raw API Response:');
                $this->line(json_encode($result, JSON_PRETTY_PRINT));
            }

        } else {
            $this->error('❌ API call failed!');
            $this->error("⏱️  Execution time: {$executionTime}ms");
            $this->newLine();

            $this->error('Error Details:');
            $this->table(
                ['Field', 'Value'],
                [
                    ['Success', $result['success'] ? 'true' : 'false'],
                    ['HTTP Status', $result['http_status'] ?? 'N/A'],
                    ['Error Message', $result['message'] ?? 'Unknown error'],
                    ['Error Code', $result['error_code'] ?? 'N/A'],
                ]
            );

            if ($verbose && isset($result['raw_response'])) {
                $this->newLine();
                $this->error('🔍 Raw Error Response:');
                $this->line(json_encode($result, JSON_PRETTY_PRINT));
            }
        }
    }
}
