<?php

namespace Modules\Fingerspot\Console\Commands;

use Illuminate\Console\Command;
use Modules\Fingerspot\Services\FingerspotApiService;
use Modules\Fingerspot\Models\AttendanceLog;
use Exception;

class TestFingerspotAttlog extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fingerspot:test-attlog
                            {--device-id= : Specific device ID to test}
                            {--start-date= : Start date (Y-m-d format)}
                            {--end-date= : End date (Y-m-d format)}
                            {--limit=10 : Number of records to fetch}
                            {--save-db : Save fetched logs to database}
                            {--verbose : Show detailed output}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Fingerspot.io get attlog API functionality and optionally save to database';

    /**
     * Create a new command instance.
     */
    public function __construct(
        private FingerspotApiService $apiService
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Testing Fingerspot.io Get Attlog API...');
        $this->newLine();

        try {
            // Get options
            $deviceId = $this->option('device-id');
            $startDate = $this->option('start-date') ?: now()->subDays(7)->format('Y-m-d');
            $endDate = $this->option('end-date') ?: now()->format('Y-m-d');
            $limit = (int) $this->option('limit');
            $verbose = $this->option('verbose');

            // Display configuration
            $this->displayConfiguration($verbose);

            // Test parameters
            $this->info('📋 Test Parameters:');
            $this->table(
                ['Parameter', 'Value'],
                [
                    ['Device ID', $deviceId ?: 'All devices'],
                    ['Start Date', $startDate],
                    ['End Date', $endDate],
                    ['Limit', $limit],
                ]
            );
            $this->newLine();

            // Execute test
            $saveToDb = $this->option('save-db');

            if ($saveToDb) {
                $this->info('🔄 Executing get attlog API call and saving to database...');
                $startTime = microtime(true);

                $result = $this->apiService->getAttendanceLogsAndSave($deviceId, $startDate, $endDate, 1, $limit);

                $executionTime = round((microtime(true) - $startTime) * 1000, 2);

                // Display results with database info
                $this->displayResultsWithDatabase($result, $executionTime, $verbose);
            } else {
                $this->info('🔄 Executing get attlog API call...');
                $startTime = microtime(true);

                $result = $this->apiService->getAttendanceLogs($deviceId, $startDate, $endDate, 1, $limit);

                $executionTime = round((microtime(true) - $startTime) * 1000, 2);

                // Display results
                $this->displayResults($result, $executionTime, $verbose);
            }

            return $result['success'] ? Command::SUCCESS : Command::FAILURE;

        } catch (Exception $e) {
            $this->error('❌ Test failed with exception:');
            $this->error($e->getMessage());
            
            if ($this->option('verbose')) {
                $this->newLine();
                $this->error('Stack trace:');
                $this->line($e->getTraceAsString());
            }

            return Command::FAILURE;
        }
    }

    /**
     * Display configuration information
     */
    private function displayConfiguration(bool $verbose): void
    {
        $this->info('⚙️  Configuration:');
        
        $config = $this->apiService->getConfiguration();
        
        $configTable = [
            ['API Base URL', $config['api_base_url']],
            ['Cloud ID', $config['cloud_id']],
            ['Server', $config['server']],
            ['API Key Configured', $config['api_key_configured'] ? '✅ Yes' : '❌ No'],
            ['Module Enabled', $config['features']['real_time_sync'] ? '✅ Yes' : '❌ No'],
        ];

        if ($verbose) {
            $configTable = array_merge($configTable, [
                ['Timeout', $config['timeout'] . 's'],
                ['Retry Attempts', $config['retry_attempts']],
                ['Debug Mode', $config['debug_mode'] ? '✅ Yes' : '❌ No'],
                ['Cache Enabled', $config['cache_enabled'] ? '✅ Yes' : '❌ No'],
            ]);
        }

        $this->table(['Setting', 'Value'], $configTable);
        $this->newLine();
    }

    /**
     * Display test results
     */
    private function displayResults(array $result, float $executionTime, bool $verbose): void
    {
        if ($result['success']) {
            $this->info('✅ API call successful!');
            $this->info("⏱️  Execution time: {$executionTime}ms");
            $this->newLine();

            // Display data summary
            $data = $result['data'] ?? [];
            $recordCount = is_array($data) ? count($data) : 0;

            $this->info('📊 Results Summary:');
            $this->table(
                ['Metric', 'Value'],
                [
                    ['Records Found', $recordCount],
                    ['HTTP Status', $result['http_status'] ?? 'N/A'],
                    ['Response Time', $executionTime . 'ms'],
                ]
            );

            // Display attendance records if any
            if ($recordCount > 0) {
                $this->newLine();
                $this->info('📋 Attendance Records:');
                
                $headers = ['#', 'Employee ID', 'Date/Time', 'Device', 'Type'];
                $rows = [];

                foreach (array_slice($data, 0, 10) as $index => $record) {
                    $rows[] = [
                        $index + 1,
                        $record['employee_id'] ?? $record['user_id'] ?? 'N/A',
                        $record['datetime'] ?? $record['timestamp'] ?? 'N/A',
                        $record['device_id'] ?? $record['device'] ?? 'N/A',
                        $record['type'] ?? $record['status'] ?? 'N/A',
                    ];
                }

                $this->table($headers, $rows);

                if ($recordCount > 10) {
                    $this->info("... and " . ($recordCount - 10) . " more records");
                }
            } else {
                $this->warn('⚠️  No attendance records found for the specified criteria.');
            }

            // Show raw response if verbose
            if ($verbose) {
                $this->newLine();
                $this->info('🔍 Raw API Response:');
                $this->line(json_encode($result, JSON_PRETTY_PRINT));
            }

        } else {
            $this->error('❌ API call failed!');
            $this->error("⏱️  Execution time: {$executionTime}ms");
            $this->newLine();

            $this->error('Error Details:');
            $this->table(
                ['Field', 'Value'],
                [
                    ['Success', $result['success'] ? 'true' : 'false'],
                    ['HTTP Status', $result['http_status'] ?? 'N/A'],
                    ['Error Message', $result['message'] ?? 'Unknown error'],
                    ['Error Code', $result['error_code'] ?? 'N/A'],
                ]
            );

            if ($verbose && isset($result['raw_response'])) {
                $this->newLine();
                $this->error('🔍 Raw Error Response:');
                $this->line(json_encode($result, JSON_PRETTY_PRINT));
            }
        }
    }

    /**
     * Display test results with database information
     */
    private function displayResultsWithDatabase(array $result, float $executionTime, bool $verbose): void
    {
        if ($result['success']) {
            $this->info('✅ API call and database save successful!');
            $this->info("⏱️  Execution time: {$executionTime}ms");
            $this->newLine();

            // Display database save summary
            $this->info('💾 Database Save Summary:');
            $this->table(
                ['Metric', 'Value'],
                [
                    ['Total Fetched', $result['total_fetched'] ?? 0],
                    ['Successfully Saved', $result['saved_count'] ?? 0],
                    ['Errors', count($result['errors'] ?? [])],
                    ['Response Time', $executionTime . 'ms'],
                ]
            );

            // Show errors if any
            if (!empty($result['errors'])) {
                $this->newLine();
                $this->warn('⚠️  Errors encountered:');
                foreach ($result['errors'] as $error) {
                    $this->error("   • {$error}");
                }
            }

            // Display recent database records
            $this->newLine();
            $this->info('📋 Recent Database Records:');

            $recentLogs = AttendanceLog::orderBy('created_at', 'desc')->limit(5)->get();

            if ($recentLogs->count() > 0) {
                $headers = ['ID', 'Employee ID', 'Device ID', 'Timestamp', 'Status'];
                $rows = [];

                foreach ($recentLogs as $log) {
                    $rows[] = [
                        $log->id,
                        $log->employee_id ?? 'N/A',
                        $log->device_id ?? 'N/A',
                        $log->timestamp->format('Y-m-d H:i:s'),
                        $log->sync_status,
                    ];
                }

                $this->table($headers, $rows);
            } else {
                $this->warn('No records found in database.');
            }

            // Show raw API response if verbose
            if ($verbose && isset($result['api_result'])) {
                $this->newLine();
                $this->info('🔍 Raw API Response:');
                $this->line(json_encode($result['api_result'], JSON_PRETTY_PRINT));
            }

        } else {
            $this->error('❌ API call or database save failed!');
            $this->error("⏱️  Execution time: {$executionTime}ms");
            $this->newLine();

            $this->error('Error Details:');
            $this->table(
                ['Field', 'Value'],
                [
                    ['Success', $result['success'] ? 'true' : 'false'],
                    ['Message', $result['message'] ?? 'Unknown error'],
                    ['API Error', $result['api_error'] ?? 'N/A'],
                    ['Saved Count', $result['saved_count'] ?? 0],
                ]
            );

            if ($verbose) {
                $this->newLine();
                $this->error('🔍 Full Error Response:');
                $this->line(json_encode($result, JSON_PRETTY_PRINT));
            }
        }
    }
}
