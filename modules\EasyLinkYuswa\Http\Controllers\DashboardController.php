<?php

namespace Modules\EasyLinkYuswa\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Illuminate\View\View;
use Modules\EasyLinkYuswa\Services\EasyLinkService;

class DashboardController extends Controller
{
    public function __construct(
        private EasyLinkService $easyLinkService
    ) {}

    /**
     * Display the main dashboard
     */
    public function index(): View
    {
        $config = $this->easyLinkService->getConfiguration();
        
        return view('easylinkyuswa::dashboard', compact('config'));
    }

    /**
     * Display module information page
     */
    public function info(): View
    {
        $config = $this->easyLinkService->getConfiguration();
        $stats = $this->easyLinkService->getModuleStats();
        
        return view('easylinkyuswa::info', compact('config', 'stats'));
    }

    /**
     * Display settings page
     */
    public function settings(): View
    {
        $config = $this->easyLinkService->getConfiguration();
        
        return view('easylinkyuswa::settings', compact('config'));
    }

    /**
     * Clear module cache
     */
    public function clearCache(): JsonResponse
    {
        try {
            // Clear Laravel cache
            \Artisan::call('cache:clear');
            \Artisan::call('config:clear');
            \Artisan::call('route:clear');
            \Artisan::call('view:clear');
            
            return response()->json([
                'success' => true,
                'message' => 'Cache cleared successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear cache: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get dashboard data for AJAX requests
     */
    public function getDashboardData(): JsonResponse
    {
        try {
            $connectivity = $this->easyLinkService->checkDeviceConnectivity();
            $stats = $this->easyLinkService->getModuleStats();
            $config = $this->easyLinkService->getConfiguration();
            
            return response()->json([
                'success' => true,
                'data' => [
                    'connectivity' => $connectivity,
                    'stats' => $stats,
                    'config' => $config,
                    'timestamp' => now()->toDateTimeString()
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get dashboard data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test module functionality
     */
    public function testModule(): JsonResponse
    {
        try {
            $results = [];
            
            // Test connectivity
            $results['connectivity'] = $this->easyLinkService->checkDeviceConnectivity();
            
            // Test device info
            try {
                $results['device_info'] = $this->easyLinkService->getDeviceInfo();
                $results['device_info_status'] = 'success';
            } catch (\Exception $e) {
                $results['device_info_status'] = 'failed';
                $results['device_info_error'] = $e->getMessage();
            }
            
            // Test attendance logs
            try {
                $results['attendance_logs'] = $this->easyLinkService->getFormattedAttendanceLogs();
                $results['attendance_logs_status'] = 'success';
            } catch (\Exception $e) {
                $results['attendance_logs_status'] = 'failed';
                $results['attendance_logs_error'] = $e->getMessage();
            }
            
            // Overall test result
            $overallSuccess = $results['connectivity']['connected'] && 
                            $results['device_info_status'] === 'success' && 
                            $results['attendance_logs_status'] === 'success';
            
            return response()->json([
                'success' => true,
                'overall_test_result' => $overallSuccess ? 'passed' : 'failed',
                'data' => $results,
                'test_timestamp' => now()->toDateTimeString()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Module test failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export configuration
     */
    public function exportConfig(): JsonResponse
    {
        try {
            $config = $this->easyLinkService->getConfiguration();
            $stats = $this->easyLinkService->getModuleStats();
            
            $exportData = [
                'module' => 'EasyLinkYuswa',
                'export_timestamp' => now()->toDateTimeString(),
                'configuration' => $config,
                'statistics' => $stats,
                'version' => '1.0.0'
            ];
            
            return response()->json([
                'success' => true,
                'data' => $exportData,
                'filename' => 'easylinkyuswa_config_' . now()->format('Y-m-d_H-i-s') . '.json'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export configuration: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get real-time status updates
     */
    public function getRealtimeStatus(): JsonResponse
    {
        try {
            $connectivity = $this->easyLinkService->checkDeviceConnectivity();
            
            return response()->json([
                'success' => true,
                'data' => [
                    'connected' => $connectivity['connected'],
                    'status' => $connectivity['status'],
                    'response_time' => $connectivity['response_time'],
                    'last_check' => $connectivity['last_check'],
                    'timestamp' => now()->toDateTimeString()
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get realtime status: ' . $e->getMessage()
            ], 500);
        }
    }
}
