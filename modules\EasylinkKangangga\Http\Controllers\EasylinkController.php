<?php

namespace Modules\EasylinkKangangga\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Modules\EasylinkKangangga\Services\AttendanceService;

class EasylinkController extends Controller
{
    public function __construct(
        private AttendanceService $attendanceService
    ) {}

    /**
     * Get device information
     */
    public function getDeviceInfo(): JsonResponse
    {
        try {
            $deviceInfo = $this->attendanceService->getDeviceInfo();
            return response()->json([
                'success' => true,
                'data' => $deviceInfo
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get attendance logs
     */
    public function getAttendanceLogs(): JsonResponse
    {
        $result = $this->attendanceService->getFormattedAttendanceLogs();
        
        return response()->json($result, $result['success'] ? 200 : 500);
    }

    /**
     * Check device connectivity
     */
    public function checkConnectivity(): JsonResponse
    {
        $result = $this->attendanceService->checkDeviceConnectivity();
        
        return response()->json($result, $result['connected'] ? 200 : 500);
    }

    /**
     * Get SDK configuration
     */
    public function getConfiguration(): JsonResponse
    {
        $config = $this->attendanceService->getConfiguration();
        
        return response()->json([
            'success' => true,
            'configuration' => $config
        ]);
    }

    /**
     * Test device connection (simple endpoint)
     */
    public function testDevice(): JsonResponse
    {
        try {
            $deviceInfo = $this->attendanceService->getDeviceInfo();
            return response()->json([
                'success' => true,
                'data' => $deviceInfo,
                'config' => [
                    'host' => config('easylinkangga.sdk_host'),
                    'sn' => config('easylinkangga.sdk_sn'),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'config' => [
                    'host' => config('easylinkangga.sdk_host'),
                    'sn' => config('easylinkangga.sdk_sn'),
                ]
            ]);
        }
    }

    /**
     * Test attendance logs (simple endpoint)
     */
    public function testAttendance(): JsonResponse
    {
        try {
            $attendanceLogs = $this->attendanceService->getNewAttendanceLogs();
            return response()->json([
                'success' => true,
                'data' => $attendanceLogs,
                'config' => [
                    'host' => config('easylinkangga.sdk_host'),
                    'sn' => config('easylinkangga.sdk_sn'),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'config' => [
                    'host' => config('easylinkangga.sdk_host'),
                    'sn' => config('easylinkangga.sdk_sn'),
                ]
            ]);
        }
    }
}
