<?php

namespace Modules\EasyLinkYuswa\Tests;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\EasyLinkYuswa\Services\EasyLinkService;

class ModuleTest extends TestCase
{
    /**
     * Test module service registration
     */
    public function test_module_service_is_registered()
    {
        $service = app(EasyLinkService::class);
        $this->assertInstanceOf(EasyLinkService::class, $service);
    }

    /**
     * Test module service alias
     */
    public function test_module_service_alias_is_registered()
    {
        $service = app('easylinkyuswa.service');
        $this->assertInstanceOf(EasyLinkService::class, $service);
    }

    /**
     * Test module configuration
     */
    public function test_module_configuration_is_loaded()
    {
        $config = config('easylinkyuswa');
        
        $this->assertIsArray($config);
        $this->assertArrayHasKey('name', $config);
        $this->assertArrayHasKey('alias', $config);
        $this->assertArrayHasKey('sdk_host', $config);
        $this->assertEquals('EasyLinkYuswa', $config['name']);
        $this->assertEquals('easylinkyuswa', $config['alias']);
    }

    /**
     * Test health check endpoint
     */
    public function test_health_check_endpoint()
    {
        $response = $this->get('/api/easylinkyuswa/health');
        
        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'module' => 'EasyLinkYuswa',
                     'status' => 'healthy',
                     'version' => '1.0.0'
                 ]);
    }

    /**
     * Test module info endpoint
     */
    public function test_module_info_endpoint()
    {
        $response = $this->get('/api/easylinkyuswa/info');
        
        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'module' => [
                         'name' => 'EasyLinkYuswa',
                         'alias' => 'easylinkyuswa',
                         'version' => '1.0.0'
                     ]
                 ]);
    }

    /**
     * Test configuration endpoint
     */
    public function test_configuration_endpoint()
    {
        $response = $this->get('/api/easylinkyuswa/config');
        
        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true
                 ])
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'module_name',
                         'module_alias',
                         'description',
                         'enabled',
                         'debug',
                         'sdk_host',
                         'timeout'
                     ]
                 ]);
    }

    /**
     * Test device connectivity endpoint
     */
    public function test_device_connectivity_endpoint()
    {
        $response = $this->get('/api/easylinkyuswa/device/connectivity');
        
        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true
                 ])
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'connected',
                         'host',
                         'serial_number',
                         'status',
                         'last_check'
                     ]
                 ]);
    }

    /**
     * Test attendance logs endpoint
     */
    public function test_attendance_logs_endpoint()
    {
        $response = $this->get('/api/easylinkyuswa/attendance/logs');
        
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'message',
                     'data'
                 ]);
    }

    /**
     * Test dashboard route
     */
    public function test_dashboard_route()
    {
        $response = $this->get('/easylinkyuswa');
        
        $response->assertStatus(200)
                 ->assertSee('EasyLink Yuswa Dashboard')
                 ->assertSee('Connection Status')
                 ->assertSee('Device Info');
    }

    /**
     * Test module info page
     */
    public function test_module_info_page()
    {
        $response = $this->get('/easylinkyuswa/info');
        
        $response->assertStatus(200)
                 ->assertSee('Module Information')
                 ->assertSee('EasyLinkYuswa');
    }

    /**
     * Test settings page
     */
    public function test_settings_page()
    {
        $response = $this->get('/easylinkyuswa/settings');
        
        $response->assertStatus(200)
                 ->assertSee('Module Settings')
                 ->assertSee('Current Configuration');
    }

    /**
     * Test service configuration method
     */
    public function test_service_configuration_method()
    {
        $service = app(EasyLinkService::class);
        $config = $service->getConfiguration();
        
        $this->assertIsArray($config);
        $this->assertArrayHasKey('module_name', $config);
        $this->assertArrayHasKey('module_alias', $config);
        $this->assertArrayHasKey('sdk_host', $config);
        $this->assertEquals('EasyLinkYuswa', $config['module_name']);
    }

    /**
     * Test service connectivity check method
     */
    public function test_service_connectivity_check_method()
    {
        $service = app(EasyLinkService::class);
        $connectivity = $service->checkDeviceConnectivity();
        
        $this->assertIsArray($connectivity);
        $this->assertArrayHasKey('connected', $connectivity);
        $this->assertArrayHasKey('host', $connectivity);
        $this->assertArrayHasKey('status', $connectivity);
        $this->assertIsBool($connectivity['connected']);
    }

    /**
     * Test service module stats method
     */
    public function test_service_module_stats_method()
    {
        $service = app(EasyLinkService::class);
        $stats = $service->getModuleStats();
        
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('module_enabled', $stats);
        $this->assertArrayHasKey('device_connected', $stats);
        $this->assertArrayHasKey('total_logs', $stats);
        $this->assertArrayHasKey('errors_count', $stats);
        $this->assertIsBool($stats['module_enabled']);
        $this->assertIsBool($stats['device_connected']);
    }

    /**
     * Test service formatted attendance logs method
     */
    public function test_service_formatted_attendance_logs_method()
    {
        $service = app(EasyLinkService::class);
        $logs = $service->getFormattedAttendanceLogs();
        
        $this->assertIsArray($logs);
        $this->assertArrayHasKey('success', $logs);
        $this->assertArrayHasKey('message', $logs);
        $this->assertArrayHasKey('data', $logs);
        $this->assertIsBool($logs['success']);
        $this->assertIsArray($logs['data']);
    }

    /**
     * Test all API endpoints are accessible
     */
    public function test_all_api_endpoints_are_accessible()
    {
        $endpoints = [
            '/api/easylinkyuswa/health',
            '/api/easylinkyuswa/info',
            '/api/easylinkyuswa/config',
            '/api/easylinkyuswa/device/connectivity',
            '/api/easylinkyuswa/device/info',
            '/api/easylinkyuswa/device/status',
            '/api/easylinkyuswa/attendance/logs',
            '/api/easylinkyuswa/config/stats'
        ];

        foreach ($endpoints as $endpoint) {
            $response = $this->get($endpoint);
            $response->assertStatus(200);
        }
    }

    /**
     * Test all web routes are accessible
     */
    public function test_all_web_routes_are_accessible()
    {
        $routes = [
            '/easylinkyuswa',
            '/easylinkyuswa/dashboard',
            '/easylinkyuswa/info',
            '/easylinkyuswa/settings'
        ];

        foreach ($routes as $route) {
            $response = $this->get($route);
            $response->assertStatus(200);
        }
    }
}
