@extends('fingerspot::layouts.master')

@section('title', 'Dashboard')

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt me-2"></i>
        Fingerspot Dashboard
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshDashboard()">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="testModule()">
                <i class="fas fa-vial"></i> Test Module
            </button>
        </div>
        <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="autoRefreshToggle" checked>
            <label class="form-check-label" for="autoRefreshToggle">
                Auto Refresh
            </label>
        </div>
    </div>
</div>

<!-- Status Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="stats-number" id="api-status">
                            <span class="loading-spinner"></span>
                        </div>
                        <div class="stats-label">API Status</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-plug fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="stats-number" id="webhooks-24h">
                            <span class="loading-spinner"></span>
                        </div>
                        <div class="stats-label">Webhooks (24h)</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-webhook fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="stats-number" id="attendance-24h">
                            <span class="loading-spinner"></span>
                        </div>
                        <div class="stats-label">Attendance (24h)</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="stats-number" id="devices-online">
                            <span class="loading-spinner"></span>
                        </div>
                        <div class="stats-label">Devices Online</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-desktop fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- API Configuration -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog me-2"></i>
                    API Configuration
                </h5>
            </div>
            <div class="card-body">
                <div id="api-config-content">
                    <div class="text-center">
                        <span class="loading-spinner"></span> Loading configuration...
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-webhook me-2"></i>
                    Webhook Configuration
                </h5>
            </div>
            <div class="card-body">
                <div id="webhook-config-content">
                    <div class="text-center">
                        <span class="loading-spinner"></span> Loading configuration...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    Recent Activity
                </h5>
            </div>
            <div class="card-body">
                <div id="recent-activity">
                    <div class="text-center">
                        <span class="loading-spinner"></span> Loading recent activity...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Results Modal -->
<div class="modal fade" id="testResultsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-vial me-2"></i>
                    Module Test Results
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="test-results-content">
                    <div class="text-center">
                        <span class="loading-spinner"></span> Running tests...
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-fingerspot" onclick="testModule()">Run Again</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Initial load
    refreshDashboard();
    
    // Auto refresh setup
    const autoRefreshToggle = $('#autoRefreshToggle');
    
    if (autoRefreshToggle.is(':checked')) {
        startAutoRefresh(refreshDashboard, 30000);
    }
    
    autoRefreshToggle.change(function() {
        if ($(this).is(':checked')) {
            startAutoRefresh(refreshDashboard, 30000);
        } else {
            stopAutoRefresh();
        }
    });
});

function refreshDashboard() {
    // Show loading states
    showLoading('#api-status');
    showLoading('#webhooks-24h');
    showLoading('#attendance-24h');
    showLoading('#devices-online');
    
    // Load dashboard data
    $.get('{{ route("fingerspot.api.dashboard.data") }}')
        .done(function(response) {
            if (response.success) {
                updateDashboardData(response.data);
            } else {
                showError('#api-status', 'Error');
                showError('#webhooks-24h', 'Error');
                showError('#attendance-24h', 'Error');
                showError('#devices-online', 'Error');
            }
        })
        .fail(function() {
            showError('#api-status', 'Error');
            showError('#webhooks-24h', 'Error');
            showError('#attendance-24h', 'Error');
            showError('#devices-online', 'Error');
        });
}

function updateDashboardData(data) {
    // Update API status
    const apiStatus = data.connectivity.connected ? 'Online' : 'Offline';
    const apiStatusClass = data.connectivity.connected ? 'status-online' : 'status-offline';
    $('#api-status').html(`<span class="${apiStatusClass}">${apiStatus}</span>`);
    
    // Update webhook count
    $('#webhooks-24h').text(data.webhook_stats.total_webhooks || 0);
    
    // Update attendance count
    $('#attendance-24h').text(data.stats.recent_attendance_24h || 0);
    
    // Update devices online (placeholder)
    $('#devices-online').text('N/A');
    
    // Update API configuration
    updateApiConfig(data.config);
    
    // Update webhook configuration
    updateWebhookConfig(data.webhook_stats);
    
    // Update recent activity
    updateRecentActivity(data.stats);
}

function updateApiConfig(config) {
    const html = `
        <div class="row">
            <div class="col-sm-6">
                <strong>Base URL:</strong><br>
                <small class="text-muted">${config.api_base_url}</small>
            </div>
            <div class="col-sm-6">
                <strong>Version:</strong><br>
                <small class="text-muted">${config.api_version}</small>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-sm-6">
                <strong>Timeout:</strong><br>
                <small class="text-muted">${config.timeout}s</small>
            </div>
            <div class="col-sm-6">
                <strong>Cache Enabled:</strong><br>
                <small class="text-muted">${config.cache_enabled ? 'Yes' : 'No'}</small>
            </div>
        </div>
    `;
    $('#api-config-content').html(html);
}

function updateWebhookConfig(webhookStats) {
    const html = `
        <div class="row">
            <div class="col-sm-6">
                <strong>Total Webhooks:</strong><br>
                <small class="text-muted">${webhookStats.total_webhooks || 0}</small>
            </div>
            <div class="col-sm-6">
                <strong>Successful:</strong><br>
                <small class="text-muted">${webhookStats.successful_webhooks || 0}</small>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-sm-6">
                <strong>Failed:</strong><br>
                <small class="text-muted">${webhookStats.failed_webhooks || 0}</small>
            </div>
            <div class="col-sm-6">
                <strong>Last 7 Days:</strong><br>
                <small class="text-muted">${webhookStats.total_webhooks || 0}</small>
            </div>
        </div>
    `;
    $('#webhook-config-content').html(html);
}

function updateRecentActivity(stats) {
    let html = '<div class="list-group list-group-flush">';
    
    if (stats.last_webhook) {
        html += `
            <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Latest Webhook</h6>
                    <small>${formatTimestamp(stats.last_webhook.received_at)}</small>
                </div>
                <p class="mb-1">Type: ${stats.last_webhook.type}</p>
                <small>Status: ${stats.last_webhook.status}</small>
            </div>
        `;
    }
    
    if (stats.last_attendance) {
        html += `
            <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">Latest Attendance</h6>
                    <small>${formatTimestamp(stats.last_attendance.timestamp)}</small>
                </div>
                <p class="mb-1">Employee: ${stats.last_attendance.employee_id || 'N/A'}</p>
                <small>Device: ${stats.last_attendance.device_id || 'N/A'}</small>
            </div>
        `;
    }
    
    if (!stats.last_webhook && !stats.last_attendance) {
        html += `
            <div class="list-group-item text-center text-muted">
                <i class="fas fa-info-circle me-2"></i>
                No recent activity
            </div>
        `;
    }
    
    html += '</div>';
    $('#recent-activity').html(html);
}

function testModule() {
    $('#testResultsModal').modal('show');
    $('#test-results-content').html('<div class="text-center"><span class="loading-spinner"></span> Running tests...</div>');
    
    $.get('{{ route("fingerspot.api.test.module") }}')
        .done(function(response) {
            if (response.success) {
                displayTestResults(response);
            } else {
                $('#test-results-content').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Test failed: ${response.message}
                    </div>
                `);
            }
        })
        .fail(function() {
            $('#test-results-content').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Failed to run tests. Please try again.
                </div>
            `);
        });
}

function displayTestResults(response) {
    const overallStatus = response.overall_test_result === 'passed' ? 'success' : 'danger';
    const overallIcon = response.overall_test_result === 'passed' ? 'check-circle' : 'times-circle';
    
    let html = `
        <div class="alert alert-${overallStatus} alert-modern">
            <h5><i class="fas fa-${overallIcon} me-2"></i>Overall Result: ${response.overall_test_result.toUpperCase()}</h5>
            <small>Test completed at: ${formatTimestamp(response.test_timestamp)}</small>
        </div>
        
        <div class="accordion" id="testAccordion">
    `;
    
    // API Connectivity Test
    const apiStatus = response.data.api_connectivity.connected ? 'success' : 'danger';
    const apiIcon = response.data.api_connectivity.connected ? 'check' : 'times';
    
    html += `
        <div class="accordion-item">
            <h2 class="accordion-header">
                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#apiTest">
                    <i class="fas fa-${apiIcon} text-${apiStatus} me-2"></i>
                    API Connectivity Test
                </button>
            </h2>
            <div id="apiTest" class="accordion-collapse collapse show" data-bs-parent="#testAccordion">
                <div class="accordion-body">
                    <p><strong>Status:</strong> ${response.data.api_connectivity.status}</p>
                    <p><strong>Response Time:</strong> ${response.data.api_connectivity.response_time}</p>
                    <p><strong>Last Check:</strong> ${formatTimestamp(response.data.api_connectivity.last_check)}</p>
                </div>
            </div>
        </div>
    `;
    
    // Device List Test
    const deviceStatus = response.data.device_list.status === 'success' ? 'success' : 'danger';
    const deviceIcon = response.data.device_list.status === 'success' ? 'check' : 'times';
    
    html += `
        <div class="accordion-item">
            <h2 class="accordion-header">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#deviceTest">
                    <i class="fas fa-${deviceIcon} text-${deviceStatus} me-2"></i>
                    Device List Test
                </button>
            </h2>
            <div id="deviceTest" class="accordion-collapse collapse" data-bs-parent="#testAccordion">
                <div class="accordion-body">
                    <p><strong>Status:</strong> ${response.data.device_list.status}</p>
                    <p><strong>Device Count:</strong> ${response.data.device_list.count || 0}</p>
                    ${response.data.device_list.error ? `<p class="text-danger"><strong>Error:</strong> ${response.data.device_list.error}</p>` : ''}
                </div>
            </div>
        </div>
    `;
    
    // Webhook Test
    const webhookStatus = response.data.webhook_test.status === 'success' ? 'success' : 'danger';
    const webhookIcon = response.data.webhook_test.status === 'success' ? 'check' : 'times';
    
    html += `
        <div class="accordion-item">
            <h2 class="accordion-header">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#webhookTest">
                    <i class="fas fa-${webhookIcon} text-${webhookStatus} me-2"></i>
                    Webhook Test
                </button>
            </h2>
            <div id="webhookTest" class="accordion-collapse collapse" data-bs-parent="#testAccordion">
                <div class="accordion-body">
                    <p><strong>Status:</strong> ${response.data.webhook_test.status}</p>
                    ${response.data.webhook_test.error ? `<p class="text-danger"><strong>Error:</strong> ${response.data.webhook_test.error}</p>` : ''}
                    ${response.data.webhook_test.result ? `<pre class="bg-light p-2 rounded"><code>${JSON.stringify(response.data.webhook_test.result, null, 2)}</code></pre>` : ''}
                </div>
            </div>
        </div>
    `;
    
    html += '</div>';
    
    $('#test-results-content').html(html);
}
</script>
@endpush
