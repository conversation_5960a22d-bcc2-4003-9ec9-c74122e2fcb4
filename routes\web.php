<?php

use Illuminate\Support\Facades\Route;
use Kangangga\EasylinkSdk\EasylinkSdkFacade as EasylinkSdk;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

// EasyLink SDK Dashboard
Route::get('/easylink-dashboard', [App\Http\Controllers\EasylinkDashboardController::class, 'index'])
    ->name('easylink.dashboard');

// Test routes for EasyLink SDK
Route::get('/test-easylink-device', function () {
    try {
        $deviceInfo = EasylinkSdk::device();
        return response()->json([
            'success' => true,
            'data' => $deviceInfo,
            'config' => [
                'host' => config('easylink.sdk_host'),
                'sn' => config('easylink.sdk_sn'),
            ]
        ]);
    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'config' => [
                'host' => config('easylink.sdk_host'),
                'sn' => config('easylink.sdk_sn'),
            ]
        ]);
    }
});

Route::get('/test-easylink-attendance', function () {
    try {
        $attendanceLogs = EasylinkSdk::scanlogNew();
        return response()->json([
            'success' => true,
            'data' => $attendanceLogs,
            'config' => [
                'host' => config('easylink.sdk_host'),
                'sn' => config('easylink.sdk_sn'),
            ]
        ]);
    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage(),
            'config' => [
                'host' => config('easylink.sdk_host'),
                'sn' => config('easylink.sdk_sn'),
            ]
        ]);
    }
});

// EasyLink Controller routes
Route::prefix('easylink')->group(function () {
    Route::get('/device-info', [App\Http\Controllers\EasylinkController::class, 'getDeviceInfo']);
    Route::get('/attendance-logs', [App\Http\Controllers\EasylinkController::class, 'getAttendanceLogs']);
    Route::get('/check-connectivity', [App\Http\Controllers\EasylinkController::class, 'checkConnectivity']);
    Route::get('/configuration', [App\Http\Controllers\EasylinkController::class, 'getConfiguration']);
});
