<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON><PERSON>\EasylinkKangangga\Http\Controllers\DashboardController;
use Modules\EasylinkKangangga\Http\Controllers\EasylinkController;

/*
|--------------------------------------------------------------------------
| EasyLink Kangangga Module Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for the EasyLink Kangangga module.
| These routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group and "easylinkangga" prefix.
|
*/

// Dashboard Routes
Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard.index');
Route::get('/info', [DashboardController::class, 'info'])->name('info');
Route::get('/settings', [DashboardController::class, 'settings'])->name('settings');

// API Routes for AJAX calls
Route::prefix('api')->name('api.')->group(function () {
    Route::get('/device-info', [EasylinkController::class, 'getDeviceInfo'])->name('device.info');
    Route::get('/attendance-logs', [EasylinkController::class, 'getAttendanceLogs'])->name('attendance.logs');
    Route::get('/check-connectivity', [EasylinkController::class, 'checkConnectivity'])->name('connectivity.check');
    Route::get('/configuration', [EasylinkController::class, 'getConfiguration'])->name('configuration');
    Route::post('/clear-cache', [DashboardController::class, 'clearCache'])->name('cache.clear');
});

// Simple Test Routes (for backward compatibility)
Route::get('/test-device', [EasylinkController::class, 'testDevice'])->name('test.device');
Route::get('/test-attendance', [EasylinkController::class, 'testAttendance'])->name('test.attendance');
