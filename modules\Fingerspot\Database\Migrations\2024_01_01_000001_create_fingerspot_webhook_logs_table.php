<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('fingerspot_webhook_logs', function (Blueprint $table) {
            $table->id();
            $table->string('type', 50)->index(); // attendance, employee, device, general
            $table->string('status', 50)->default('received')->index(); // received, processed, failed, signature_verification_failed
            $table->string('url', 500);
            $table->string('method', 10)->default('POST');
            $table->json('headers')->nullable();
            $table->longText('payload')->nullable();
            $table->string('ip_address', 45)->nullable();
            $table->string('user_agent', 500)->nullable();
            $table->timestamp('received_at')->index();
            $table->timestamp('processed_at')->nullable();
            $table->text('error_message')->nullable();
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['type', 'status']);
            $table->index(['received_at', 'type']);
            $table->index(['status', 'received_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('fingerspot_webhook_logs');
    }
};
