<?php

namespace Modules\Fingerspot\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Modules\Fingerspot\Services\FingerspotWebhookService;
use Exception;

class FingerspotWebhookController extends Controller
{
    public function __construct(
        private FingerspotWebhookService $webhookService
    ) {}

    /**
     * Handle attendance webhook
     */
    public function handleAttendanceWebhook(Request $request): JsonResponse
    {
        try {
            $result = $this->webhookService->processWebhook($request, 'attendance');
            
            $statusCode = $result['success'] ? 200 : ($result['status_code'] ?? 500);
            
            return response()->json($result, $statusCode);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Webhook processing failed',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle employee webhook
     */
    public function handleEmployeeWebhook(Request $request): JsonResponse
    {
        try {
            $result = $this->webhookService->processWebhook($request, 'employee');
            
            $statusCode = $result['success'] ? 200 : ($result['status_code'] ?? 500);
            
            return response()->json($result, $statusCode);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Webhook processing failed',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle device webhook
     */
    public function handleDeviceWebhook(Request $request): JsonResponse
    {
        try {
            $result = $this->webhookService->processWebhook($request, 'device');
            
            $statusCode = $result['success'] ? 200 : ($result['status_code'] ?? 500);
            
            return response()->json($result, $statusCode);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Webhook processing failed',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle general webhook
     */
    public function handleGeneralWebhook(Request $request): JsonResponse
    {
        try {
            $result = $this->webhookService->processWebhook($request, 'general');
            
            $statusCode = $result['success'] ? 200 : ($result['status_code'] ?? 500);
            
            return response()->json($result, $statusCode);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Webhook processing failed',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get webhook statistics
     */
    public function getWebhookStats(Request $request): JsonResponse
    {
        try {
            $days = $request->get('days', 7);
            $stats = $this->webhookService->getWebhookStats($days);
            
            return response()->json([
                'success' => true,
                'data' => $stats,
                'period_days' => $days,
                'timestamp' => now()->toDateTimeString()
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get webhook configuration
     */
    public function getWebhookConfiguration(): JsonResponse
    {
        try {
            $config = $this->webhookService->getWebhookConfiguration();
            
            return response()->json([
                'success' => true,
                'configuration' => $config,
                'timestamp' => now()->toDateTimeString()
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test webhook endpoint
     */
    public function testWebhookEndpoint(Request $request): JsonResponse
    {
        try {
            $type = $request->get('type', 'general');
            $result = $this->webhookService->testWebhookEndpoint($type);
            
            return response()->json([
                'success' => true,
                'test_result' => $result,
                'type' => $type,
                'timestamp' => now()->toDateTimeString()
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
