<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Exception;

class FingerspotApiService
{
    protected $client;
    protected $baseUrl;
    protected $apiKey;
    protected $secretKey;
    protected $timeout;

    public function __construct()
    {
        $this->baseUrl = config('fingerspot.api_base_url', 'https://developer.fingerspot.io/api');
        $this->apiKey = config('fingerspot.api_key');
        $this->secretKey = config('fingerspot.secret_key');
        $this->timeout = config('fingerspot.timeout', 30);

        $this->client = new Client([
            'base_uri' => $this->baseUrl,
            'timeout' => $this->timeout,
            'verify' => false,
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'X-API-Key' => $this->api<PERSON>ey,
            ]
        ]);
    }

    /**
     * Generate authentication signature
     */
    protected function generateSignature($method, $endpoint, $timestamp, $body = '')
    {
        $stringToSign = $method . "\n" . $endpoint . "\n" . $timestamp . "\n" . $body;
        return hash_hmac('sha256', $stringToSign, $this->secretKey);
    }

    /**
     * Make authenticated API request
     */
    protected function makeRequest($method, $endpoint, $data = [], $options = [])
    {
        try {
            $timestamp = time();
            $body = !empty($data) ? json_encode($data) : '';
            $signature = $this->generateSignature($method, $endpoint, $timestamp, $body);

            $headers = [
                'X-Timestamp' => $timestamp,
                'X-Signature' => $signature,
            ];

            $requestOptions = array_merge([
                'headers' => $headers,
            ], $options);

            if (!empty($data)) {
                $requestOptions['json'] = $data;
            }

            $response = $this->client->request($method, $endpoint, $requestOptions);
            $responseBody = $response->getBody()->getContents();

            return [
                'success' => true,
                'status_code' => $response->getStatusCode(),
                'data' => json_decode($responseBody, true),
                'raw_response' => $responseBody
            ];

        } catch (RequestException $e) {
            $this->logError("API request failed: {$method} {$endpoint}", $e);
            
            $response = $e->getResponse();
            $statusCode = $response ? $response->getStatusCode() : 0;
            $responseBody = $response ? $response->getBody()->getContents() : '';

            return [
                'success' => false,
                'status_code' => $statusCode,
                'error' => $e->getMessage(),
                'response' => $responseBody ? json_decode($responseBody, true) : null
            ];
        } catch (Exception $e) {
            $this->logError("API request exception: {$method} {$endpoint}", $e);
            
            return [
                'success' => false,
                'status_code' => 0,
                'error' => $e->getMessage(),
                'response' => null
            ];
        }
    }

    /**
     * Get device list
     */
    public function getDevices($page = 1, $limit = 50)
    {
        return $this->makeRequest('GET', '/devices', [], [
            'query' => [
                'page' => $page,
                'limit' => $limit
            ]
        ]);
    }

    /**
     * Get device information
     */
    public function getDevice($deviceId)
    {
        return $this->makeRequest('GET', "/devices/{$deviceId}");
    }

    /**
     * Get device status
     */
    public function getDeviceStatus($deviceId)
    {
        return $this->makeRequest('GET', "/devices/{$deviceId}/status");
    }

    /**
     * Get attendance logs
     */
    public function getAttendanceLogs($deviceId = null, $startDate = null, $endDate = null, $page = 1, $limit = 100)
    {
        $params = [
            'page' => $page,
            'limit' => $limit
        ];

        if ($deviceId) {
            $params['device_id'] = $deviceId;
        }

        if ($startDate) {
            $params['start_date'] = $startDate;
        }

        if ($endDate) {
            $params['end_date'] = $endDate;
        }

        return $this->makeRequest('GET', '/attendance/logs', [], [
            'query' => $params
        ]);
    }

    /**
     * Get user/employee list
     */
    public function getUsers($deviceId = null, $page = 1, $limit = 100)
    {
        $params = [
            'page' => $page,
            'limit' => $limit
        ];

        if ($deviceId) {
            $params['device_id'] = $deviceId;
        }

        return $this->makeRequest('GET', '/users', [], [
            'query' => $params
        ]);
    }

    /**
     * Create or update user
     */
    public function createUser($userData)
    {
        return $this->makeRequest('POST', '/users', $userData);
    }

    /**
     * Update user
     */
    public function updateUser($userId, $userData)
    {
        return $this->makeRequest('PUT', "/users/{$userId}", $userData);
    }

    /**
     * Delete user
     */
    public function deleteUser($userId)
    {
        return $this->makeRequest('DELETE', "/users/{$userId}");
    }

    /**
     * Sync user to device
     */
    public function syncUserToDevice($deviceId, $userId)
    {
        return $this->makeRequest('POST', "/devices/{$deviceId}/users/{$userId}/sync");
    }

    /**
     * Get device logs
     */
    public function getDeviceLogs($deviceId, $startDate = null, $endDate = null, $page = 1, $limit = 100)
    {
        $params = [
            'page' => $page,
            'limit' => $limit
        ];

        if ($startDate) {
            $params['start_date'] = $startDate;
        }

        if ($endDate) {
            $params['end_date'] = $endDate;
        }

        return $this->makeRequest('GET', "/devices/{$deviceId}/logs", [], [
            'query' => $params
        ]);
    }

    /**
     * Send command to device
     */
    public function sendDeviceCommand($deviceId, $command, $parameters = [])
    {
        return $this->makeRequest('POST', "/devices/{$deviceId}/commands", [
            'command' => $command,
            'parameters' => $parameters
        ]);
    }

    /**
     * Get webhook configurations
     */
    public function getWebhooks()
    {
        return $this->makeRequest('GET', '/webhooks');
    }

    /**
     * Create webhook
     */
    public function createWebhook($webhookData)
    {
        return $this->makeRequest('POST', '/webhooks', $webhookData);
    }

    /**
     * Update webhook
     */
    public function updateWebhook($webhookId, $webhookData)
    {
        return $this->makeRequest('PUT', "/webhooks/{$webhookId}", $webhookData);
    }

    /**
     * Delete webhook
     */
    public function deleteWebhook($webhookId)
    {
        return $this->makeRequest('DELETE', "/webhooks/{$webhookId}");
    }

    /**
     * Test webhook
     */
    public function testWebhook($webhookId)
    {
        return $this->makeRequest('POST', "/webhooks/{$webhookId}/test");
    }

    /**
     * Get API usage statistics
     */
    public function getApiUsage($startDate = null, $endDate = null)
    {
        $params = [];

        if ($startDate) {
            $params['start_date'] = $startDate;
        }

        if ($endDate) {
            $params['end_date'] = $endDate;
        }

        return $this->makeRequest('GET', '/usage', [], [
            'query' => $params
        ]);
    }

    /**
     * Log error messages
     */
    protected function logError($message, Exception $exception)
    {
        Log::error("[FingerspotAPI] {$message}", [
            'exception' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
            'api_key' => substr($this->apiKey, 0, 8) . '...',
        ]);
    }

    /**
     * Get cached data or fetch from API
     */
    public function getCachedData($cacheKey, $callback, $ttl = 300)
    {
        return Cache::remember($cacheKey, $ttl, $callback);
    }

    /**
     * Clear specific cache
     */
    public function clearCache($pattern = 'fingerspot_api_*')
    {
        Cache::flush();
    }
}
