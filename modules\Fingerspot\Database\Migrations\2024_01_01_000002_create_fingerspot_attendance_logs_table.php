<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('fingerspot_attendance_logs', function (Blueprint $table) {
            $table->id();
            $table->string('device_id', 100)->nullable()->index();
            $table->string('user_id', 100)->nullable()->index();
            $table->string('employee_id', 100)->nullable()->index();
            $table->timestamp('timestamp')->index();
            $table->string('event_type', 100)->default('attendance.created')->index(); // attendance.created, attendance.updated, attendance.deleted
            $table->json('raw_data')->nullable();
            $table->timestamp('processed_at')->nullable();
            $table->string('sync_status', 50)->default('pending')->index(); // pending, synced, failed
            $table->text('error_message')->nullable();
            $table->text('fingerprint_template')->nullable();
            $table->string('verification_method', 50)->nullable(); // fingerprint, face, card, password
            $table->string('location', 255)->nullable();
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['device_id', 'timestamp']);
            $table->index(['employee_id', 'timestamp']);
            $table->index(['event_type', 'timestamp']);
            $table->index(['sync_status', 'timestamp']);
            $table->index(['timestamp', 'device_id', 'employee_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('fingerspot_attendance_logs');
    }
};
