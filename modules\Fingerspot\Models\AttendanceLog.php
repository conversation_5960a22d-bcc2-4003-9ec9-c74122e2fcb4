<?php

namespace Modules\Fingerspot\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class AttendanceLog extends Model
{
    use HasFactory;

    protected $table = 'fingerspot_attendance_logs';

    protected $fillable = [
        'device_id',
        'user_id',
        'employee_id',
        'timestamp',
        'event_type',
        'raw_data',
        'processed_at',
        'sync_status',
        'fingerprint_template',
        'verification_method',
        'location',
    ];

    protected $casts = [
        'raw_data' => 'array',
        'timestamp' => 'datetime',
        'processed_at' => 'datetime',
    ];

    /**
     * Scope for recent logs
     */
    public function scopeRecent($query, $days = 7)
    {
        return $query->where('timestamp', '>=', now()->subDays($days));
    }

    /**
     * Scope by device
     */
    public function scopeByDevice($query, $deviceId)
    {
        return $query->where('device_id', $deviceId);
    }

    /**
     * Scope by employee
     */
    public function scopeByEmployee($query, $employeeId)
    {
        return $query->where('employee_id', $employeeId);
    }

    /**
     * Scope by event type
     */
    public function scopeByEventType($query, $eventType)
    {
        return $query->where('event_type', $eventType);
    }

    /**
     * Scope for synced logs
     */
    public function scopeSynced($query)
    {
        return $query->where('sync_status', 'synced');
    }

    /**
     * Scope for unsynced logs
     */
    public function scopeUnsynced($query)
    {
        return $query->where('sync_status', '!=', 'synced');
    }

    /**
     * Get formatted raw data
     */
    public function getFormattedRawDataAttribute()
    {
        if (is_array($this->raw_data)) {
            return json_encode($this->raw_data, JSON_PRETTY_PRINT);
        }
        
        return $this->raw_data;
    }

    /**
     * Check if log is synced
     */
    public function isSynced()
    {
        return $this->sync_status === 'synced';
    }

    /**
     * Mark as synced
     */
    public function markAsSynced()
    {
        $this->update(['sync_status' => 'synced']);
    }

    /**
     * Mark as failed
     */
    public function markAsFailed($reason = null)
    {
        $this->update([
            'sync_status' => 'failed',
            'error_message' => $reason
        ]);
    }
}
