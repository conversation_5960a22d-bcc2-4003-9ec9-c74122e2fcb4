<?php

namespace Modules\EasyLinkYuswa\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Exception;

class EasyLinkService
{
    protected $client;
    protected $config;
    protected $host;
    protected $serialNumber;
    protected $timeout;

    public function __construct()
    {
        $this->config = config('easylinkyuswa');
        $this->host = $this->config['sdk_host'];
        $this->serialNumber = $this->config['sdk_sn'];
        $this->timeout = $this->config['timeout'];
        
        $this->client = new Client([
            'timeout' => $this->timeout,
            'verify' => false,
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ]
        ]);
    }

    /**
     * Get device information
     *
     * @return Collection
     * @throws Exception
     */
    public function getDeviceInfo(): Collection
    {
        try {
            $response = $this->makeRequest('GET', '/device/info');
            
            return collect([
                'Result' => true,
                'Data' => $response,
                'Message' => 'Device information retrieved successfully'
            ]);
        } catch (Exception $e) {
            $this->logError('Failed to get device info', $e);
            throw new Exception("Failed to get device info: " . $e->getMessage());
        }
    }

    /**
     * Get new attendance logs
     *
     * @return Collection
     * @throws Exception
     */
    public function getNewAttendanceLogs(): Collection
    {
        try {
            $response = $this->makeRequest('GET', '/attendance/logs');
            
            return collect([
                'Result' => true,
                'Data' => $response['data'] ?? [],
                'Count' => count($response['data'] ?? []),
                'Message' => 'Attendance logs retrieved successfully'
            ]);
        } catch (Exception $e) {
            $this->logError('Failed to get attendance logs', $e);
            throw new Exception("Failed to get attendance logs: " . $e->getMessage());
        }
    }

    /**
     * Check device connectivity
     *
     * @return array
     */
    public function checkDeviceConnectivity(): array
    {
        try {
            $startTime = microtime(true);
            $response = $this->makeRequest('GET', '/device/ping');
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            return [
                'connected' => true,
                'response_time' => $responseTime . 'ms',
                'host' => $this->host,
                'serial_number' => $this->serialNumber,
                'status' => 'Online',
                'last_check' => now()->toDateTimeString()
            ];
        } catch (Exception $e) {
            $this->logError('Device connectivity check failed', $e);
            
            return [
                'connected' => false,
                'response_time' => null,
                'host' => $this->host,
                'serial_number' => $this->serialNumber,
                'status' => 'Offline',
                'error' => $e->getMessage(),
                'last_check' => now()->toDateTimeString()
            ];
        }
    }

    /**
     * Get formatted attendance logs
     *
     * @return array
     */
    public function getFormattedAttendanceLogs(): array
    {
        try {
            $logs = $this->getNewAttendanceLogs();
            
            if (!$logs->get('Result')) {
                return [
                    'success' => false,
                    'message' => 'No attendance data available',
                    'data' => []
                ];
            }

            $attendanceData = collect($logs->get('Data', []))->map(function ($log) {
                return [
                    'employee_id' => $log['PIN'] ?? $log['employee_id'] ?? null,
                    'nip' => $log['nip'] ?? $log['PIN'] ?? $log['employee_id'] ?? null,
                    'scan_date' => $log['ScanDate'] ?? $log['scan_date'] ?? $log['timestamp'] ?? null,
                    'date' => $log['date'] ?? $log['ScanDate'] ?? $log['scan_date'] ?? null,
                    'time' => $log['time'] ?? null,
                    'status' => $log['status'] ?? $log['Status'] ?? null,
                    'device_id' => $log['device_id'] ?? $this->serialNumber,
                    'raw_data' => $log
                ];
            });

            return [
                'success' => true,
                'message' => 'Attendance logs retrieved successfully',
                'count' => $attendanceData->count(),
                'data' => $attendanceData->toArray()
            ];
        } catch (Exception $e) {
            $this->logError('Failed to format attendance logs', $e);
            
            return [
                'success' => false,
                'message' => 'Failed to retrieve attendance logs: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * Get module configuration
     *
     * @return array
     */
    public function getConfiguration(): array
    {
        return [
            'module_name' => $this->config['name'],
            'module_alias' => $this->config['alias'],
            'description' => $this->config['description'],
            'enabled' => $this->config['enabled'],
            'debug' => $this->config['debug'],
            'sdk_host' => $this->config['sdk_host'],
            'sdk_sn' => $this->config['sdk_sn'],
            'server_port' => $this->config['server_port'],
            'timeout' => $this->config['timeout'],
            'auto_refresh' => $this->config['auto_refresh'],
            'refresh_interval' => $this->config['refresh_interval'],
            'api_prefix' => $this->config['api']['prefix'],
            'web_prefix' => $this->config['web']['prefix'],
        ];
    }

    /**
     * Make HTTP request to EasyLink device
     *
     * @param string $method
     * @param string $endpoint
     * @param array $data
     * @return array
     * @throws Exception
     */
    protected function makeRequest(string $method, string $endpoint, array $data = []): array
    {
        try {
            $url = rtrim($this->host, '/') . $endpoint;
            
            $options = [];
            if (!empty($data)) {
                $options['json'] = $data;
            }

            $response = $this->client->request($method, $url, $options);
            $body = $response->getBody()->getContents();
            
            return json_decode($body, true) ?? [];
        } catch (RequestException $e) {
            $this->logError("HTTP request failed: {$method} {$endpoint}", $e);
            throw new Exception("HTTP request failed: " . $e->getMessage());
        } catch (Exception $e) {
            $this->logError("Request processing failed: {$method} {$endpoint}", $e);
            throw $e;
        }
    }

    /**
     * Log error messages
     *
     * @param string $message
     * @param Exception $exception
     */
    protected function logError(string $message, Exception $exception): void
    {
        if ($this->config['logging']['enabled']) {
            Log::channel($this->config['logging']['channel'])
                ->error("[EasyLinkYuswa] {$message}", [
                    'exception' => $exception->getMessage(),
                    'trace' => $exception->getTraceAsString(),
                    'host' => $this->host,
                    'serial_number' => $this->serialNumber
                ]);
        }
    }

    /**
     * Get module statistics
     *
     * @return array
     */
    public function getModuleStats(): array
    {
        $stats = [
            'module_enabled' => $this->config['enabled'],
            'device_connected' => false,
            'last_sync' => null,
            'total_logs' => 0,
            'errors_count' => 0,
        ];

        try {
            $connectivity = $this->checkDeviceConnectivity();
            $stats['device_connected'] = $connectivity['connected'];
            
            if ($connectivity['connected']) {
                $logs = $this->getFormattedAttendanceLogs();
                $stats['total_logs'] = $logs['count'] ?? 0;
                $stats['last_sync'] = now()->toDateTimeString();
            }
        } catch (Exception $e) {
            $stats['errors_count']++;
            $this->logError('Failed to get module stats', $e);
        }

        return $stats;
    }
}
