<?php

namespace Modules\Fingerspot\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\Fingerspot\Models\AttendanceLog;

class AttendanceReceived implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $attendanceLog;
    public $payload;
    public $timestamp;

    /**
     * Create a new event instance.
     */
    public function __construct(AttendanceLog $attendanceLog, array $payload)
    {
        $this->attendanceLog = $attendanceLog;
        $this->payload = $payload;
        $this->timestamp = now()->toDateTimeString();
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('fingerspot.attendance'),
            new PrivateChannel('fingerspot.attendance.device.' . $this->attendanceLog->device_id),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'attendance_log' => [
                'id' => $this->attendanceLog->id,
                'device_id' => $this->attendanceLog->device_id,
                'employee_id' => $this->attendanceLog->employee_id,
                'event_type' => $this->attendanceLog->event_type,
                'timestamp' => $this->attendanceLog->timestamp,
            ],
            'payload' => $this->payload,
            'timestamp' => $this->timestamp,
            'event_name' => 'attendance.received'
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'attendance.received';
    }
}
