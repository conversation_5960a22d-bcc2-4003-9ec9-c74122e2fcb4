<?php $__env->startSection('title', 'Webhooks - Fingerspot Module'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-webhook me-2"></i>
                        Webhook Management
                    </h3>
                    <div>
                        <button type="button" class="btn btn-primary" onclick="refreshWebhooks()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#webhookModal">
                            <i class="fas fa-plus"></i> Add Webhook
                        </button>
                        <button type="button" class="btn btn-info" onclick="testWebhook()">
                            <i class="fas fa-vial"></i> Test Webhook
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Webhook Configuration -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-cog"></i> Webhook Configuration
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-sm">
                                                <tr>
                                                    <td><strong>Webhook URL:</strong></td>
                                                    <td>
                                                        <code id="webhookUrl"><?php echo e(url('/webhook/fingerspot/attendance')); ?></code>
                                                        <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('#webhookUrl')">
                                                            <i class="fas fa-copy"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Status:</strong></td>
                                                    <td><span class="badge bg-success" id="webhookStatus">Active</span></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Secret Key:</strong></td>
                                                    <td>
                                                        <code><?php echo e(config('fingerspot.webhook.secret_key', 'Not configured')); ?></code>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <table class="table table-sm">
                                                <tr>
                                                    <td><strong>Events:</strong></td>
                                                    <td>
                                                        <span class="badge bg-primary me-1">attendance</span>
                                                        <span class="badge bg-info me-1">employee</span>
                                                        <span class="badge bg-warning me-1">device</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Format:</strong></td>
                                                    <td>JSON</td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Timeout:</strong></td>
                                                    <td>30 seconds</td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Webhook Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="totalWebhooks">-</h4>
                                            <p class="mb-0">Total Webhooks</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-webhook fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="successfulWebhooks">-</h4>
                                            <p class="mb-0">Successful</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-check-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="failedWebhooks">-</h4>
                                            <p class="mb-0">Failed</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="lastWebhook">-</h4>
                                            <p class="mb-0">Last Received</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-clock fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Webhook Logs -->
                    <div class="row">
                        <div class="col-12">
                            <h5>Recent Webhook Logs</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="webhooksTable">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>ID</th>
                                            <th>Event Type</th>
                                            <th>Source</th>
                                            <th>Status</th>
                                            <th>Received At</th>
                                            <th>Response Time</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="webhooksTableBody">
                                        <tr>
                                            <td colspan="7" class="text-center">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                                <p class="mt-2">Loading webhook logs...</p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Webhook Modal -->
<div class="modal fade" id="webhookModal" tabindex="-1" aria-labelledby="webhookModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="webhookModalLabel">Add Webhook Endpoint</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="webhookForm">
                    <div class="mb-3">
                        <label for="webhookName" class="form-label">Name</label>
                        <input type="text" class="form-control" id="webhookName" placeholder="Enter webhook name" required>
                    </div>
                    <div class="mb-3">
                        <label for="webhookUrl" class="form-label">URL</label>
                        <input type="url" class="form-control" id="webhookUrlInput" placeholder="https://example.com/webhook" required>
                    </div>
                    <div class="mb-3">
                        <label for="webhookEvents" class="form-label">Events</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="attendance" id="eventAttendance" checked>
                            <label class="form-check-label" for="eventAttendance">Attendance</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="employee" id="eventEmployee">
                            <label class="form-check-label" for="eventEmployee">Employee</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="device" id="eventDevice">
                            <label class="form-check-label" for="eventDevice">Device</label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="webhookSecret" class="form-label">Secret Key (Optional)</label>
                        <input type="text" class="form-control" id="webhookSecret" placeholder="Enter secret key for verification">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="saveWebhook()">Save Webhook</button>
            </div>
        </div>
    </div>
</div>

<!-- Webhook Details Modal -->
<div class="modal fade" id="webhookDetailsModal" tabindex="-1" aria-labelledby="webhookDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="webhookDetailsModalLabel">Webhook Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="webhookDetailsModalBody">
                <!-- Webhook details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-warning" onclick="retryWebhook()">Retry</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
$(document).ready(function() {
    loadWebhooks();
    loadStatistics();
    
    // Auto-refresh every 30 seconds
    setInterval(function() {
        loadWebhooks();
        loadStatistics();
    }, 30000);
});

function loadWebhooks() {
    $.get('/fingerspot/api/webhooks')
        .done(function(response) {
            if (response.success && response.data) {
                displayWebhooks(response.data);
            } else {
                showError('Failed to load webhooks: ' + (response.message || 'Unknown error'));
            }
        })
        .fail(function(xhr) {
            showError('Failed to load webhooks: ' + (xhr.responseJSON?.message || 'Network error'));
        });
}

function displayWebhooks(webhooks) {
    const tbody = $('#webhooksTableBody');
    tbody.empty();
    
    if (!webhooks || webhooks.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="7" class="text-center text-muted">
                    <i class="fas fa-info-circle"></i> No webhook logs found
                </td>
            </tr>
        `);
        return;
    }
    
    webhooks.forEach(function(webhook) {
        const statusBadge = getWebhookStatusBadge(webhook.status);
        const receivedAt = new Date(webhook.received_at).toLocaleString();
        const responseTime = webhook.response_time ? webhook.response_time + 'ms' : 'N/A';
        
        tbody.append(`
            <tr>
                <td><strong>${webhook.id}</strong></td>
                <td><span class="badge bg-primary">${webhook.event_type}</span></td>
                <td>${webhook.source || 'Fingerspot'}</td>
                <td>${statusBadge}</td>
                <td>${receivedAt}</td>
                <td>${responseTime}</td>
                <td>
                    <button class="btn btn-sm btn-info" onclick="viewWebhook(${webhook.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${webhook.status === 'failed' ? `<button class="btn btn-sm btn-warning" onclick="retryWebhook(${webhook.id})"><i class="fas fa-redo"></i></button>` : ''}
                </td>
            </tr>
        `);
    });
}

function getWebhookStatusBadge(status) {
    switch (status) {
        case 'success':
            return '<span class="badge bg-success">Success</span>';
        case 'failed':
            return '<span class="badge bg-danger">Failed</span>';
        case 'pending':
            return '<span class="badge bg-warning">Pending</span>';
        default:
            return '<span class="badge bg-secondary">Unknown</span>';
    }
}

function loadStatistics() {
    $.get('/fingerspot/api/webhooks/statistics')
        .done(function(response) {
            if (response.success) {
                $('#totalWebhooks').text(response.data.total || 0);
                $('#successfulWebhooks').text(response.data.successful || 0);
                $('#failedWebhooks').text(response.data.failed || 0);
                $('#lastWebhook').text(response.data.last_received || 'Never');
            }
        })
        .fail(function() {
            // Silently fail for statistics
        });
}

function refreshWebhooks() {
    loadWebhooks();
    loadStatistics();
}

function testWebhook() {
    if (confirm('This will send a test webhook payload. Continue?')) {
        $.post('/fingerspot/api/webhooks/test')
            .done(function(response) {
                if (response.success) {
                    showSuccess('Test webhook sent successfully');
                    loadWebhooks();
                } else {
                    showError('Failed to send test webhook: ' + (response.message || 'Unknown error'));
                }
            })
            .fail(function() {
                showError('Failed to send test webhook');
            });
    }
}

function saveWebhook() {
    const formData = {
        name: $('#webhookName').val(),
        url: $('#webhookUrlInput').val(),
        events: [],
        secret: $('#webhookSecret').val()
    };
    
    // Get selected events
    $('input[type="checkbox"]:checked').each(function() {
        formData.events.push($(this).val());
    });
    
    if (!formData.name || !formData.url) {
        showError('Please fill in all required fields');
        return;
    }
    
    $.post('/fingerspot/api/webhooks', formData)
        .done(function(response) {
            if (response.success) {
                showSuccess('Webhook saved successfully');
                $('#webhookModal').modal('hide');
                $('#webhookForm')[0].reset();
                loadWebhooks();
            } else {
                showError('Failed to save webhook: ' + (response.message || 'Unknown error'));
            }
        })
        .fail(function() {
            showError('Failed to save webhook');
        });
}

function viewWebhook(webhookId) {
    $('#webhookDetailsModalLabel').text(`Webhook #${webhookId}`);
    $('#webhookDetailsModalBody').html('<div class="text-center"><div class="spinner-border" role="status"></div></div>');
    $('#webhookDetailsModal').modal('show');
    
    $.get(`/fingerspot/api/webhooks/${webhookId}`)
        .done(function(response) {
            if (response.success) {
                displayWebhookDetails(response.data);
            } else {
                $('#webhookDetailsModalBody').html(`<div class="alert alert-danger">Failed to load webhook details</div>`);
            }
        })
        .fail(function() {
            $('#webhookDetailsModalBody').html(`<div class="alert alert-danger">Failed to load webhook details</div>`);
        });
}

function displayWebhookDetails(webhook) {
    const html = `
        <div class="row">
            <div class="col-md-6">
                <h6>Basic Information</h6>
                <table class="table table-sm">
                    <tr><td><strong>ID:</strong></td><td>${webhook.id}</td></tr>
                    <tr><td><strong>Event Type:</strong></td><td><span class="badge bg-primary">${webhook.event_type}</span></td></tr>
                    <tr><td><strong>Source:</strong></td><td>${webhook.source || 'Fingerspot'}</td></tr>
                    <tr><td><strong>Status:</strong></td><td>${getWebhookStatusBadge(webhook.status)}</td></tr>
                    <tr><td><strong>Received At:</strong></td><td>${new Date(webhook.received_at).toLocaleString()}</td></tr>
                    <tr><td><strong>Response Time:</strong></td><td>${webhook.response_time ? webhook.response_time + 'ms' : 'N/A'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Response Information</h6>
                <table class="table table-sm">
                    <tr><td><strong>HTTP Status:</strong></td><td>${webhook.http_status || 'N/A'}</td></tr>
                    <tr><td><strong>Error Message:</strong></td><td>${webhook.error_message || 'None'}</td></tr>
                    <tr><td><strong>Retry Count:</strong></td><td>${webhook.retry_count || 0}</td></tr>
                    <tr><td><strong>Next Retry:</strong></td><td>${webhook.next_retry_at ? new Date(webhook.next_retry_at).toLocaleString() : 'N/A'}</td></tr>
                </table>
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-12">
                <h6>Payload</h6>
                <pre class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;">${JSON.stringify(webhook.payload, null, 2)}</pre>
            </div>
        </div>
        
        ${webhook.response ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6>Response</h6>
                <pre class="bg-light p-3 rounded" style="max-height: 200px; overflow-y: auto;">${webhook.response}</pre>
            </div>
        </div>
        ` : ''}
    `;
    $('#webhookDetailsModalBody').html(html);
}

function retryWebhook(webhookId) {
    if (confirm('Retry this webhook?')) {
        $.post(`/fingerspot/api/webhooks/${webhookId}/retry`)
            .done(function(response) {
                if (response.success) {
                    showSuccess('Webhook retry initiated');
                    loadWebhooks();
                } else {
                    showError('Failed to retry webhook: ' + (response.message || 'Unknown error'));
                }
            })
            .fail(function() {
                showError('Failed to retry webhook');
            });
    }
}

function copyToClipboard(element) {
    const text = $(element).text();
    navigator.clipboard.writeText(text).then(function() {
        showSuccess('Copied to clipboard');
    }).catch(function() {
        showError('Failed to copy to clipboard');
    });
}

function showSuccess(message) {
    // You can implement a toast notification here
    alert('Success: ' + message);
}

function showError(message) {
    // You can implement a toast notification here
    alert('Error: ' + message);
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('fingerspot::layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\larattendance\modules\Fingerspot\Providers/../Resources/views/webhooks.blade.php ENDPATH**/ ?>