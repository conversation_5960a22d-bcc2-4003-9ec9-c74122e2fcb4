<?php

namespace Modules\EasyLinkYuswa\Console;

use Illuminate\Console\Command;
use Modules\EasyLinkYuswa\Services\EasyLinkService;

class TestEasyLinkCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'easylinkyuswa:test {--device : Test device connectivity} {--attendance : Test attendance logs} {--all : Test all functionality}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test EasyLink Yuswa module functionality';

    /**
     * The EasyLink service instance.
     *
     * @var EasyLinkService
     */
    protected $easyLinkService;

    /**
     * Create a new command instance.
     *
     * @param EasyLinkService $easyLinkService
     */
    public function __construct(EasyLinkService $easyLinkService)
    {
        parent::__construct();
        $this->easyLinkService = $easyLinkService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('EasyLink Yuswa Module Test');
        $this->info('========================');

        $testDevice = $this->option('device');
        $testAttendance = $this->option('attendance');
        $testAll = $this->option('all');

        if (!$testDevice && !$testAttendance && !$testAll) {
            $testAll = true; // Default to testing all if no specific option is provided
        }

        $results = [];

        if ($testAll || $testDevice) {
            $results['device'] = $this->testDeviceConnectivity();
        }

        if ($testAll || $testAttendance) {
            $results['attendance'] = $this->testAttendanceLogs();
        }

        if ($testAll) {
            $results['configuration'] = $this->testConfiguration();
            $results['stats'] = $this->testModuleStats();
        }

        $this->displayResults($results);

        return 0;
    }

    /**
     * Test device connectivity
     *
     * @return array
     */
    protected function testDeviceConnectivity(): array
    {
        $this->info('Testing device connectivity...');
        
        try {
            $connectivity = $this->easyLinkService->checkDeviceConnectivity();
            
            if ($connectivity['connected']) {
                $this->info('✓ Device connectivity: PASSED');
                $this->line("  - Host: {$connectivity['host']}");
                $this->line("  - Serial Number: {$connectivity['serial_number']}");
                $this->line("  - Response Time: {$connectivity['response_time']}");
                $this->line("  - Status: {$connectivity['status']}");
                
                return ['status' => 'passed', 'data' => $connectivity];
            } else {
                $this->error('✗ Device connectivity: FAILED');
                $this->line("  - Error: {$connectivity['error']}");
                
                return ['status' => 'failed', 'error' => $connectivity['error']];
            }
        } catch (\Exception $e) {
            $this->error('✗ Device connectivity: ERROR');
            $this->line("  - Exception: {$e->getMessage()}");
            
            return ['status' => 'error', 'error' => $e->getMessage()];
        }
    }

    /**
     * Test attendance logs retrieval
     *
     * @return array
     */
    protected function testAttendanceLogs(): array
    {
        $this->info('Testing attendance logs retrieval...');
        
        try {
            $logs = $this->easyLinkService->getFormattedAttendanceLogs();
            
            if ($logs['success']) {
                $this->info('✓ Attendance logs: PASSED');
                $this->line("  - Records retrieved: {$logs['count']}");
                $this->line("  - Message: {$logs['message']}");
                
                if ($logs['count'] > 0) {
                    $this->line('  - Sample record:');
                    $sample = $logs['data'][0];
                    $this->line("    Employee ID: " . ($sample['employee_id'] ?? 'N/A'));
                    $this->line("    Scan Date: " . ($sample['scan_date'] ?? 'N/A'));
                }
                
                return ['status' => 'passed', 'data' => $logs];
            } else {
                $this->error('✗ Attendance logs: FAILED');
                $this->line("  - Message: {$logs['message']}");
                
                return ['status' => 'failed', 'error' => $logs['message']];
            }
        } catch (\Exception $e) {
            $this->error('✗ Attendance logs: ERROR');
            $this->line("  - Exception: {$e->getMessage()}");
            
            return ['status' => 'error', 'error' => $e->getMessage()];
        }
    }

    /**
     * Test configuration retrieval
     *
     * @return array
     */
    protected function testConfiguration(): array
    {
        $this->info('Testing configuration...');
        
        try {
            $config = $this->easyLinkService->getConfiguration();
            
            $this->info('✓ Configuration: PASSED');
            $this->line("  - Module: {$config['module_name']}");
            $this->line("  - Alias: {$config['module_alias']}");
            $this->line("  - Enabled: " . ($config['enabled'] ? 'Yes' : 'No'));
            $this->line("  - SDK Host: {$config['sdk_host']}");
            $this->line("  - Serial Number: {$config['sdk_sn']}");
            $this->line("  - Timeout: {$config['timeout']}s");
            
            return ['status' => 'passed', 'data' => $config];
        } catch (\Exception $e) {
            $this->error('✗ Configuration: ERROR');
            $this->line("  - Exception: {$e->getMessage()}");
            
            return ['status' => 'error', 'error' => $e->getMessage()];
        }
    }

    /**
     * Test module statistics
     *
     * @return array
     */
    protected function testModuleStats(): array
    {
        $this->info('Testing module statistics...');
        
        try {
            $stats = $this->easyLinkService->getModuleStats();
            
            $this->info('✓ Module statistics: PASSED');
            $this->line("  - Module Enabled: " . ($stats['module_enabled'] ? 'Yes' : 'No'));
            $this->line("  - Device Connected: " . ($stats['device_connected'] ? 'Yes' : 'No'));
            $this->line("  - Total Logs: {$stats['total_logs']}");
            $this->line("  - Errors Count: {$stats['errors_count']}");
            $this->line("  - Last Sync: " . ($stats['last_sync'] ?? 'Never'));
            
            return ['status' => 'passed', 'data' => $stats];
        } catch (\Exception $e) {
            $this->error('✗ Module statistics: ERROR');
            $this->line("  - Exception: {$e->getMessage()}");
            
            return ['status' => 'error', 'error' => $e->getMessage()];
        }
    }

    /**
     * Display test results summary
     *
     * @param array $results
     */
    protected function displayResults(array $results): void
    {
        $this->info('');
        $this->info('Test Results Summary');
        $this->info('==================');

        $totalTests = count($results);
        $passedTests = 0;
        $failedTests = 0;
        $errorTests = 0;

        foreach ($results as $testName => $result) {
            $status = $result['status'];
            $icon = $status === 'passed' ? '✓' : ($status === 'failed' ? '✗' : '⚠');
            $color = $status === 'passed' ? 'info' : ($status === 'failed' ? 'error' : 'warn');
            
            $this->{$color}("{$icon} {$testName}: " . strtoupper($status));
            
            if ($status === 'passed') {
                $passedTests++;
            } elseif ($status === 'failed') {
                $failedTests++;
            } else {
                $errorTests++;
            }
        }

        $this->info('');
        $this->info("Total Tests: {$totalTests}");
        $this->info("Passed: {$passedTests}");
        
        if ($failedTests > 0) {
            $this->error("Failed: {$failedTests}");
        }
        
        if ($errorTests > 0) {
            $this->warn("Errors: {$errorTests}");
        }

        if ($passedTests === $totalTests) {
            $this->info('🎉 All tests passed!');
        } else {
            $this->warn('⚠ Some tests failed. Please check the configuration and device connectivity.');
        }
    }
}
