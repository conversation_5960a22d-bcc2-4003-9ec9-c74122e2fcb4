<?php

namespace Modules\Fingerspot\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class DeviceLog extends Model
{
    use HasFactory;

    protected $table = 'fingerspot_device_logs';

    protected $fillable = [
        'device_id',
        'event_type',
        'status',
        'message',
        'raw_data',
        'timestamp',
        'processed_at',
        'ip_address',
        'firmware_version',
        'battery_level',
        'memory_usage',
    ];

    protected $casts = [
        'raw_data' => 'array',
        'timestamp' => 'datetime',
        'processed_at' => 'datetime',
        'battery_level' => 'integer',
        'memory_usage' => 'integer',
    ];

    /**
     * Scope for recent logs
     */
    public function scopeRecent($query, $days = 7)
    {
        return $query->where('timestamp', '>=', now()->subDays($days));
    }

    /**
     * Scope by device
     */
    public function scopeByDevice($query, $deviceId)
    {
        return $query->where('device_id', $deviceId);
    }

    /**
     * Scope by event type
     */
    public function scopeByEventType($query, $eventType)
    {
        return $query->where('event_type', $eventType);
    }

    /**
     * Scope by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for online devices
     */
    public function scopeOnline($query)
    {
        return $query->where('status', 'online');
    }

    /**
     * Scope for offline devices
     */
    public function scopeOffline($query)
    {
        return $query->where('status', 'offline');
    }

    /**
     * Get formatted raw data
     */
    public function getFormattedRawDataAttribute()
    {
        if (is_array($this->raw_data)) {
            return json_encode($this->raw_data, JSON_PRETTY_PRINT);
        }
        
        return $this->raw_data;
    }

    /**
     * Check if device is online
     */
    public function isOnline()
    {
        return $this->status === 'online';
    }

    /**
     * Check if device is offline
     */
    public function isOffline()
    {
        return $this->status === 'offline';
    }

    /**
     * Get battery status
     */
    public function getBatteryStatus()
    {
        if ($this->battery_level >= 80) {
            return 'high';
        } elseif ($this->battery_level >= 50) {
            return 'medium';
        } elseif ($this->battery_level >= 20) {
            return 'low';
        } else {
            return 'critical';
        }
    }

    /**
     * Get memory status
     */
    public function getMemoryStatus()
    {
        if ($this->memory_usage <= 50) {
            return 'normal';
        } elseif ($this->memory_usage <= 80) {
            return 'high';
        } else {
            return 'critical';
        }
    }
}
