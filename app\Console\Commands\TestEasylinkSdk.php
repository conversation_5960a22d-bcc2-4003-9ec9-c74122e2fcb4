<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Kangangga\EasylinkSdk\EasylinkSdkFacade as EasylinkSdk;

class TestEasylinkSdk extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'easylink:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test EasyLink SDK configuration and connectivity';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing EasyLink SDK Configuration...');

        // Display current configuration
        $this->info('Configuration:');
        $this->line('  Host: ' . config('easylink.sdk_host'));
        $this->line('  Serial Number: ' . config('easylink.sdk_sn'));
        $this->line('  Server Port: ' . config('easylink.server_port'));
        $this->line('  DB Host: ' . config('easylink.db_host'));
        $this->line('  DB Database: ' . config('easylink.db_database'));

        $this->newLine();

        // Test device info
        $this->info('Testing device info...');
        try {
            $deviceInfo = EasylinkSdk::device();
            $this->info('✓ Device info retrieved successfully');
            $this->line('Response: ' . json_encode($deviceInfo->toArray(), JSON_PRETTY_PRINT));
        } catch (\Exception $e) {
            $this->error('✗ Failed to get device info: ' . $e->getMessage());
        }

        $this->newLine();

        // Test attendance logs
        $this->info('Testing attendance logs...');
        try {
            $attendanceLogs = EasylinkSdk::scanlogNew();
            $this->info('✓ Attendance logs retrieved successfully');
            $this->line('Response: ' . json_encode($attendanceLogs->toArray(), JSON_PRETTY_PRINT));
        } catch (\Exception $e) {
            $this->error('✗ Failed to get attendance logs: ' . $e->getMessage());
        }

        $this->newLine();
        $this->info('EasyLink SDK test completed!');
    }
}
