@extends('fingerspot::layouts.master')

@section('title', 'Settings')

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-cog me-2"></i>
        Fingerspot Settings
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="testConnection()">
                <i class="fas fa-plug"></i> Test Connection
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="testWebhook()">
                <i class="fas fa-webhook"></i> Test Webhook
            </button>
        </div>
    </div>
</div>

<!-- Configuration Notice -->
<div class="alert alert-info alert-modern mb-4">
    <h5><i class="fas fa-info-circle me-2"></i>Configuration Notice</h5>
    <p class="mb-0">
        Fingerspot module configuration is managed through environment variables in your <code>.env</code> file. 
        Please update the configuration values there and restart your application for changes to take effect.
    </p>
</div>

<!-- Current Configuration -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-server me-2"></i>
                    API Configuration
                </h5>
            </div>
            <div class="card-body">
                <div id="api-config-display">
                    <div class="text-center">
                        <span class="loading-spinner"></span> Loading configuration...
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-webhook me-2"></i>
                    Webhook Configuration
                </h5>
            </div>
            <div class="card-body">
                <div id="webhook-config-display">
                    <div class="text-center">
                        <span class="loading-spinner"></span> Loading configuration...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Environment Variables Guide -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-code me-2"></i>
                    Environment Variables Guide
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>API Configuration</h6>
                        <pre class="bg-light p-3 rounded"><code># Fingerspot API Configuration
FINGERSPOT_API_BASE_URL=https://developer.fingerspot.io/api
FINGERSPOT_API_VERSION=v1
FINGERSPOT_API_KEY=your_api_key_here
FINGERSPOT_SECRET_KEY=your_secret_key_here
FINGERSPOT_CLIENT_ID=your_client_id_here
FINGERSPOT_CLIENT_SECRET=your_client_secret_here

# API Settings
FINGERSPOT_API_TIMEOUT=30
FINGERSPOT_API_RETRY_ATTEMPTS=3
FINGERSPOT_API_RETRY_DELAY=1000
FINGERSPOT_RATE_LIMIT=60
FINGERSPOT_CACHE_TTL=300</code></pre>
                    </div>
                    <div class="col-md-6">
                        <h6>Webhook Configuration</h6>
                        <pre class="bg-light p-3 rounded"><code># Webhook Configuration
FINGERSPOT_WEBHOOK_ENABLED=true
FINGERSPOT_WEBHOOK_SECRET=your_webhook_secret_here
FINGERSPOT_WEBHOOK_VERIFY_SIGNATURE=true

# Webhook Endpoints
FINGERSPOT_WEBHOOK_ATTENDANCE_ENDPOINT=/webhook/fingerspot/attendance
FINGERSPOT_WEBHOOK_EMPLOYEE_ENDPOINT=/webhook/fingerspot/employee
FINGERSPOT_WEBHOOK_DEVICE_ENDPOINT=/webhook/fingerspot/device
FINGERSPOT_WEBHOOK_GENERAL_ENDPOINT=/webhook/fingerspot/general</code></pre>
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>Module Settings</h6>
                        <pre class="bg-light p-3 rounded"><code># Module Settings
FINGERSPOT_MODULE_ENABLED=true
FINGERSPOT_DEBUG=false
FINGERSPOT_LOGGING_ENABLED=true
FINGERSPOT_LOG_LEVEL=info
FINGERSPOT_LOG_CHANNEL=single

# Database Configuration
FINGERSPOT_DB_CONNECTION=mysql
FINGERSPOT_TABLE_PREFIX=fingerspot_
FINGERSPOT_STORE_RAW_DATA=true
FINGERSPOT_CLEANUP_OLD_DATA=true
FINGERSPOT_CLEANUP_DAYS=90</code></pre>
                    </div>
                    <div class="col-md-6">
                        <h6>Feature Flags</h6>
                        <pre class="bg-light p-3 rounded"><code># Feature Flags
FINGERSPOT_REAL_TIME_SYNC=true
FINGERSPOT_EMPLOYEE_MANAGEMENT=true
FINGERSPOT_DEVICE_MANAGEMENT=true
FINGERSPOT_ATTENDANCE_REPORTS=true
FINGERSPOT_WEBHOOK_RETRY=true
FINGERSPOT_API_CACHING=true

# UI Configuration
FINGERSPOT_UI_THEME=modern
FINGERSPOT_UI_AUTO_REFRESH=true
FINGERSPOT_UI_REFRESH_INTERVAL=30
FINGERSPOT_UI_ITEMS_PER_PAGE=25</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Webhook Endpoints -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-link me-2"></i>
                    Webhook Endpoints
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted mb-3">
                    Configure these URLs in your Fingerspot Developer Portal to receive webhooks:
                </p>
                
                <div class="table-responsive">
                    <table class="table table-modern">
                        <thead>
                            <tr>
                                <th>Type</th>
                                <th>URL</th>
                                <th>Method</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><span class="badge bg-primary">Attendance</span></td>
                                <td><code>{{ url('/webhook/fingerspot/attendance') }}</code></td>
                                <td><span class="badge bg-success">POST</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="copyToClipboard('{{ url('/webhook/fingerspot/attendance') }}')">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-info">Employee</span></td>
                                <td><code>{{ url('/webhook/fingerspot/employee') }}</code></td>
                                <td><span class="badge bg-success">POST</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="copyToClipboard('{{ url('/webhook/fingerspot/employee') }}')">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-warning">Device</span></td>
                                <td><code>{{ url('/webhook/fingerspot/device') }}</code></td>
                                <td><span class="badge bg-success">POST</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="copyToClipboard('{{ url('/webhook/fingerspot/device') }}')">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-secondary">General</span></td>
                                <td><code>{{ url('/webhook/fingerspot/general') }}</code></td>
                                <td><span class="badge bg-success">POST</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="copyToClipboard('{{ url('/webhook/fingerspot/general') }}')">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Results Modal -->
<div class="modal fade" id="testResultsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-vial me-2"></i>
                    Test Results
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="test-results-content">
                    <div class="text-center">
                        <span class="loading-spinner"></span> Running test...
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    loadConfiguration();
});

function loadConfiguration() {
    // Load API configuration
    $.get('{{ route("fingerspot.api.configuration") }}')
        .done(function(response) {
            if (response.success) {
                displayApiConfig(response.configuration);
            } else {
                $('#api-config-display').html('<div class="text-danger">Failed to load API configuration</div>');
            }
        })
        .fail(function() {
            $('#api-config-display').html('<div class="text-danger">Failed to load API configuration</div>');
        });
    
    // Load webhook configuration
    $.get('{{ route("fingerspot.api.webhooks.config") }}')
        .done(function(response) {
            if (response.success) {
                displayWebhookConfig(response.configuration);
            } else {
                $('#webhook-config-display').html('<div class="text-danger">Failed to load webhook configuration</div>');
            }
        })
        .fail(function() {
            $('#webhook-config-display').html('<div class="text-danger">Failed to load webhook configuration</div>');
        });
}

function displayApiConfig(config) {
    const html = `
        <table class="table table-sm">
            <tr>
                <td><strong>Base URL:</strong></td>
                <td><small>${config.api_base_url}</small></td>
            </tr>
            <tr>
                <td><strong>Version:</strong></td>
                <td>${config.api_version}</td>
            </tr>
            <tr>
                <td><strong>Timeout:</strong></td>
                <td>${config.timeout}s</td>
            </tr>
            <tr>
                <td><strong>Retry Attempts:</strong></td>
                <td>${config.retry_attempts}</td>
            </tr>
            <tr>
                <td><strong>Cache Enabled:</strong></td>
                <td>${config.cache_enabled ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-secondary">No</span>'}</td>
            </tr>
            <tr>
                <td><strong>Debug Mode:</strong></td>
                <td>${config.debug_mode ? '<span class="badge bg-warning">Yes</span>' : '<span class="badge bg-secondary">No</span>'}</td>
            </tr>
        </table>
    `;
    $('#api-config-display').html(html);
}

function displayWebhookConfig(config) {
    const html = `
        <table class="table table-sm">
            <tr>
                <td><strong>Enabled:</strong></td>
                <td>${config.enabled ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-secondary">No</span>'}</td>
            </tr>
            <tr>
                <td><strong>Signature Verification:</strong></td>
                <td>${config.verify_signature ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-secondary">No</span>'}</td>
            </tr>
            <tr>
                <td><strong>Secret Configured:</strong></td>
                <td>${config.secret_configured ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-warning">No</span>'}</td>
            </tr>
            <tr>
                <td><strong>Logging Enabled:</strong></td>
                <td>${config.logging_enabled ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-secondary">No</span>'}</td>
            </tr>
            <tr>
                <td><strong>Store Raw Data:</strong></td>
                <td>${config.store_raw_data ? '<span class="badge bg-success">Yes</span>' : '<span class="badge bg-secondary">No</span>'}</td>
            </tr>
            <tr>
                <td><strong>Enabled Events:</strong></td>
                <td><span class="badge bg-info">${config.enabled_events.length} events</span></td>
            </tr>
        </table>
    `;
    $('#webhook-config-display').html(html);
}

function testConnection() {
    $('#testResultsModal').modal('show');
    $('#test-results-content').html('<div class="text-center"><span class="loading-spinner"></span> Testing API connection...</div>');
    
    $.get('{{ route("fingerspot.api.connectivity") }}')
        .done(function(response) {
            if (response.success) {
                const connectivity = response.connectivity;
                const statusClass = connectivity.connected ? 'success' : 'danger';
                const statusIcon = connectivity.connected ? 'check-circle' : 'times-circle';
                
                const html = `
                    <div class="alert alert-${statusClass} alert-modern">
                        <h5><i class="fas fa-${statusIcon} me-2"></i>Connection Test Result</h5>
                        <p><strong>Status:</strong> ${connectivity.status}</p>
                        <p><strong>Response Time:</strong> ${connectivity.response_time}</p>
                        <p><strong>Last Check:</strong> ${formatTimestamp(connectivity.last_check)}</p>
                        ${connectivity.error ? `<p class="text-danger"><strong>Error:</strong> ${connectivity.error}</p>` : ''}
                    </div>
                `;
                $('#test-results-content').html(html);
            } else {
                $('#test-results-content').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Connection test failed: ${response.message || 'Unknown error'}
                    </div>
                `);
            }
        })
        .fail(function() {
            $('#test-results-content').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Failed to test connection. Please check your configuration.
                </div>
            `);
        });
}

function testWebhook() {
    $('#testResultsModal').modal('show');
    $('#test-results-content').html('<div class="text-center"><span class="loading-spinner"></span> Testing webhook endpoint...</div>');
    
    $.get('{{ route("fingerspot.api.webhooks.test") }}')
        .done(function(response) {
            if (response.success) {
                const html = `
                    <div class="alert alert-success alert-modern">
                        <h5><i class="fas fa-check-circle me-2"></i>Webhook Test Result</h5>
                        <p><strong>Type:</strong> ${response.type}</p>
                        <p><strong>Timestamp:</strong> ${formatTimestamp(response.timestamp)}</p>
                        <pre class="bg-light p-2 rounded mt-2"><code>${JSON.stringify(response.test_result, null, 2)}</code></pre>
                    </div>
                `;
                $('#test-results-content').html(html);
            } else {
                $('#test-results-content').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Webhook test failed: ${response.error || 'Unknown error'}
                    </div>
                `);
            }
        })
        .fail(function() {
            $('#test-results-content').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Failed to test webhook. Please check your configuration.
                </div>
            `);
        });
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check me-2"></i>URL copied to clipboard!
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        document.body.appendChild(toast);
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // Remove toast after it's hidden
        toast.addEventListener('hidden.bs.toast', function() {
            document.body.removeChild(toast);
        });
    }).catch(function(err) {
        alert('Failed to copy URL to clipboard');
    });
}
</script>
@endpush
