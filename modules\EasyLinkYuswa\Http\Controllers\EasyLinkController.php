<?php

namespace Modules\EasyLinkYuswa\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\EasyLinkYuswa\Services\EasyLinkService;

class EasyLinkController extends Controller
{
    public function __construct(
        private EasyLinkService $easyLinkService
    ) {}

    /**
     * Get device information
     */
    public function getDeviceInfo(): JsonResponse
    {
        try {
            $deviceInfo = $this->easyLinkService->getDeviceInfo();
            return response()->json([
                'success' => true,
                'data' => $deviceInfo
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get attendance logs
     */
    public function getAttendanceLogs(): JsonResponse
    {
        $result = $this->easyLinkService->getFormattedAttendanceLogs();
        
        return response()->json($result, $result['success'] ? 200 : 500);
    }

    /**
     * Check device connectivity
     */
    public function checkConnectivity(): JsonResponse
    {
        $connectivity = $this->easyLinkService->checkDeviceConnectivity();
        
        return response()->json([
            'success' => true,
            'data' => $connectivity
        ]);
    }

    /**
     * Test device functionality
     */
    public function testDevice(): JsonResponse
    {
        try {
            $deviceInfo = $this->easyLinkService->getDeviceInfo();
            $connectivity = $this->easyLinkService->checkDeviceConnectivity();
            $attendanceLogs = $this->easyLinkService->getFormattedAttendanceLogs();

            return response()->json([
                'success' => true,
                'data' => [
                    'device_info' => $deviceInfo,
                    'connectivity' => $connectivity,
                    'attendance_logs' => $attendanceLogs,
                    'test_timestamp' => now()->toDateTimeString()
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Device test failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test attendance functionality
     */
    public function testAttendance(): JsonResponse
    {
        try {
            $result = $this->easyLinkService->getFormattedAttendanceLogs();
            
            return response()->json([
                'success' => true,
                'message' => 'Attendance test completed',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Attendance test failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get module configuration
     */
    public function getConfiguration(): JsonResponse
    {
        try {
            $config = $this->easyLinkService->getConfiguration();
            
            return response()->json([
                'success' => true,
                'data' => $config
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get configuration: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get module statistics
     */
    public function getStats(): JsonResponse
    {
        try {
            $stats = $this->easyLinkService->getModuleStats();
            
            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Sync attendance data
     */
    public function syncAttendance(Request $request): JsonResponse
    {
        try {
            $logs = $this->easyLinkService->getFormattedAttendanceLogs();
            
            // Here you can add logic to save the logs to your database
            // For now, we'll just return the formatted data
            
            return response()->json([
                'success' => true,
                'message' => 'Attendance data synchronized successfully',
                'data' => $logs,
                'sync_timestamp' => now()->toDateTimeString()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Sync failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get device status
     */
    public function getDeviceStatus(): JsonResponse
    {
        try {
            $connectivity = $this->easyLinkService->checkDeviceConnectivity();
            $config = $this->easyLinkService->getConfiguration();
            
            return response()->json([
                'success' => true,
                'data' => [
                    'connectivity' => $connectivity,
                    'configuration' => $config,
                    'module_enabled' => $config['enabled'],
                    'status_check_time' => now()->toDateTimeString()
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get device status: ' . $e->getMessage()
            ], 500);
        }
    }
}
