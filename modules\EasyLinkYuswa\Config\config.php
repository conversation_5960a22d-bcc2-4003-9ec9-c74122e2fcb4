<?php

return [
    'name' => 'EasyLinkYuswa',
    'alias' => 'easylinkyuswa',
    'description' => 'EasyLink SDK Module by <PERSON><PERSON><PERSON>rba - Fingerspot EasyLink Device Integration',
    
    /*
    |--------------------------------------------------------------------------
    | EasyLink SDK Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for the EasyLink SDK integration with Fingerspot devices.
    | These values are read from environment variables for security.
    |
    */
    
    'sdk_host' => env('EASYLINK_SDK_HOST', 'http://*************:5005'),
    'sdk_sn' => env('EASYLINK_SDK_SN', ''),
    'server_port' => env('EASYLINK_SERVER_PORT', 7005),
    
    /*
    |--------------------------------------------------------------------------
    | Database Configuration
    |--------------------------------------------------------------------------
    |
    | Database connection settings for the EasyLink device database.
    |
    */
    
    'db_host' => env('EASYLINK_DB_HOST', 'localhost'),
    'db_database' => env('EASYLINK_DB_DATABASE', 'fingerspot'),
    'db_username' => env('EASYLINK_DB_USERNAME', 'root'),
    'db_password' => env('EASYLINK_DB_PASSWORD', ''),
    'db_port' => env('EASYLINK_DB_PORT', 3306),
    
    /*
    |--------------------------------------------------------------------------
    | Module Settings
    |--------------------------------------------------------------------------
    |
    | General module configuration settings.
    |
    */
    
    'enabled' => env('EASYLINK_MODULE_ENABLED', true),
    'debug' => env('EASYLINK_DEBUG', false),
    'timeout' => env('EASYLINK_TIMEOUT', 30),
    'auto_refresh' => env('EASYLINK_AUTO_REFRESH', true),
    'refresh_interval' => env('EASYLINK_REFRESH_INTERVAL', 30),
    
    /*
    |--------------------------------------------------------------------------
    | API Settings
    |--------------------------------------------------------------------------
    |
    | API configuration for EasyLink device communication.
    |
    */
    
    'api' => [
        'version' => 'v1',
        'prefix' => 'easylinkyuswa',
        'middleware' => ['api'],
        'rate_limit' => 60, // requests per minute
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Web Interface Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for the web dashboard interface.
    |
    */
    
    'web' => [
        'prefix' => 'easylinkyuswa',
        'middleware' => ['web'],
        'dashboard_title' => 'EasyLink Yuswa Dashboard',
        'theme' => 'default',
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Device Communication Settings
    |--------------------------------------------------------------------------
    |
    | Settings for device communication and data processing.
    |
    */
    
    'device' => [
        'max_retries' => 3,
        'retry_delay' => 1000, // milliseconds
        'connection_timeout' => 10, // seconds
        'read_timeout' => 30, // seconds
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Logging settings for the EasyLink module.
    |
    */
    
    'logging' => [
        'enabled' => env('EASYLINK_LOGGING_ENABLED', true),
        'level' => env('EASYLINK_LOG_LEVEL', 'info'),
        'channel' => env('EASYLINK_LOG_CHANNEL', 'single'),
    ],
];
