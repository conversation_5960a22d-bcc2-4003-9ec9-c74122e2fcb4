<?php

use Illuminate\Support\Facades\Route;
use Mo<PERSON>les\Fingerspot\Http\Controllers\FingerspotDashboardController;
use Modules\Fingerspot\Http\Controllers\FingerspotApiController;
use Modules\Fingerspot\Http\Controllers\FingerspotWebhookController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your Fingerspot module.
| These routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group.
|
*/

Route::prefix('fingerspot')->name('fingerspot.')->group(function () {
    
    // Dashboard Routes
    Route::get('/', [FingerspotDashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard', [FingerspotDashboardController::class, 'index'])->name('dashboard.index');
    Route::get('/info', [FingerspotDashboardController::class, 'info'])->name('info');
    Route::get('/settings', [FingerspotDashboardController::class, 'settings'])->name('settings');
    Route::get('/devices', [FingerspotDashboardController::class, 'devices'])->name('devices');
    Route::get('/attendance', [FingerspotDashboardController::class, 'attendance'])->name('attendance');
    Route::get('/webhooks', [FingerspotDashboardController::class, 'webhooks'])->name('webhooks');

    // API Routes for AJAX calls
    Route::prefix('api')->name('api.')->group(function () {
        
        // Dashboard API
        Route::get('/dashboard-data', [FingerspotDashboardController::class, 'getDashboardData'])->name('dashboard.data');
        Route::get('/realtime-status', [FingerspotDashboardController::class, 'getRealtimeStatus'])->name('realtime.status');
        Route::get('/test-module', [FingerspotDashboardController::class, 'testModule'])->name('test.module');
        Route::post('/clear-cache', [FingerspotDashboardController::class, 'clearCache'])->name('cache.clear');
        Route::get('/export-config', [FingerspotDashboardController::class, 'exportConfig'])->name('config.export');
        
        // Device API
        Route::prefix('devices')->name('devices.')->group(function () {
            Route::get('/', [FingerspotApiController::class, 'getDevices'])->name('list');
            Route::get('/{deviceId}', [FingerspotApiController::class, 'getDevice'])->name('show');
            Route::get('/{deviceId}/status', [FingerspotApiController::class, 'getDeviceStatus'])->name('status');
            Route::post('/{deviceId}/command', [FingerspotApiController::class, 'sendDeviceCommand'])->name('command');
            Route::post('/{deviceId}/users/{userId}/sync', [FingerspotApiController::class, 'syncUserToDevice'])->name('sync.user');
        });
        
        // User/Employee API
        Route::prefix('users')->name('users.')->group(function () {
            Route::get('/', [FingerspotApiController::class, 'getUsers'])->name('list');
            Route::post('/', [FingerspotApiController::class, 'createUser'])->name('create');
            Route::put('/{userId}', [FingerspotApiController::class, 'updateUser'])->name('update');
            Route::delete('/{userId}', [FingerspotApiController::class, 'deleteUser'])->name('delete');
        });
        
        // Attendance API
        Route::prefix('attendance')->name('attendance.')->group(function () {
            Route::get('/logs', [FingerspotApiController::class, 'getAttendanceLogs'])->name('logs');
        });
        
        // Webhook API
        Route::prefix('webhooks')->name('webhooks.')->group(function () {
            Route::get('/stats', [FingerspotWebhookController::class, 'getWebhookStats'])->name('stats');
            Route::get('/config', [FingerspotWebhookController::class, 'getWebhookConfiguration'])->name('config');
            Route::get('/test', [FingerspotWebhookController::class, 'testWebhookEndpoint'])->name('test');
        });
        
        // General API
        Route::get('/connectivity', [FingerspotApiController::class, 'testConnectivity'])->name('connectivity');
        Route::get('/configuration', [FingerspotApiController::class, 'getConfiguration'])->name('configuration');
        Route::get('/usage', [FingerspotApiController::class, 'getApiUsage'])->name('usage');
        Route::post('/cache/clear', [FingerspotApiController::class, 'clearCache'])->name('cache.clear.api');

        // Test endpoints
        Route::get('/test/config', function() {
            return response()->json([
                'success' => true,
                'config' => [
                    'api_base_url' => config('fingerspot.api.base_url'),
                    'api_key' => config('fingerspot.auth.api_key') ? 'Configured' : 'Not configured',
                    'cloud_id' => config('fingerspot.fingerspot.cloud_id'),
                    'server' => config('fingerspot.fingerspot.server'),
                    'server_port' => config('fingerspot.fingerspot.server_port'),
                ],
                'env_values' => [
                    'FINGERSPOT_API_BASE_URL' => env('FINGERSPOT_API_BASE_URL'),
                    'FINGERSPOT_API_KEY' => env('FINGERSPOT_API_KEY') ? 'Configured' : 'Not configured',
                    'FINGERSPOT_CLOUDID' => env('FINGERSPOT_CLOUDID'),
                    'FINGERSPOT_SERVER' => env('FINGERSPOT_SERVER'),
                    'FINGERSPOT_SERVERPORT' => env('FINGERSPOT_SERVERPORT'),
                ]
            ]);
        })->name('test.config');
    });
});
