<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Config;
use Kangangga\EasylinkSdk\EasylinkSdk;

class EasylinkServiceProvider extends ServiceProvider
{
    public function register()
    {
        // Merge the local easylink config
        $this->mergeConfigFrom(
            __DIR__.'/../../config/easylink.php', 'easylink'
        );

        // Map our local config to the package's expected config structure
        Config::set('laravel-easylink.sdk.host', config('easylink.sdk_host'));
        Config::set('laravel-easylink.sdk.serial_number', config('easylink.sdk_sn'));
        Config::set('laravel-easylink.database.host', config('easylink.db_host'));
        Config::set('laravel-easylink.database.database', config('easylink.db_database'));
        Config::set('laravel-easylink.database.port', config('easylink.server_port'));

        // Override the package's service binding to use our local config
        $this->app->bind('laravel-easylink', function () {
            return new EasylinkSdk(
                config('easylink.sdk_host'),
                config('easylink.sdk_sn'),
            );
        });
    }

    public function boot()
    {
        $this->publishes([
            __DIR__.'/../../config/easylink.php' => config_path('easylink.php'),
        ], 'easylink-config');
    }
}
