<?php

namespace App\Services;

use Kangangga\EasylinkSdk\EasylinkSdkFacade as EasylinkSdk;
use Illuminate\Support\Collection;
use Exception;

class AttendanceService
{
    /**
     * Get device information
     *
     * @return Collection
     * @throws Exception
     */
    public function getDeviceInfo(): Collection
    {
        try {
            return EasylinkSdk::device();
        } catch (Exception $e) {
            throw new Exception("Failed to get device info: " . $e->getMessage());
        }
    }

    /**
     * Get new attendance logs
     *
     * @return Collection
     * @throws Exception
     */
    public function getNewAttendanceLogs(): Collection
    {
        try {
            return EasylinkSdk::scanlogNew();
        } catch (Exception $e) {
            throw new Exception("Failed to get attendance logs: " . $e->getMessage());
        }
    }

    /**
     * Get all attendance logs with paging
     *
     * @return Collection
     * @throws Exception
     */
    public function getAllAttendanceLogs(): Collection
    {
        try {
            return EasylinkSdk::scanlogAll();
        } catch (Exception $e) {
            throw new Exception("Failed to get all attendance logs: " . $e->getMessage());
        }
    }

    /**
     * Process attendance logs and return formatted data
     *
     * @return array
     */
    public function getFormattedAttendanceLogs(): array
    {
        try {
            $logs = $this->getNewAttendanceLogs();
            
            if (!$logs->get('Result')) {
                return [
                    'success' => false,
                    'message' => 'No attendance data available',
                    'data' => []
                ];
            }

            $attendanceData = collect($logs->get('Data', []))->map(function ($log) {
                return [
                    'employee_id' => $log['PIN'] ?? null,
                    'nip' => $log['nip'] ?? $log['PIN'] ?? null,
                    'scan_date' => $log['ScanDate'] ?? null,
                    'date' => $log['date'] ?? $log['ScanDate'] ?? null,
                    'raw_data' => $log
                ];
            });

            return [
                'success' => true,
                'message' => 'Attendance logs retrieved successfully',
                'count' => $attendanceData->count(),
                'data' => $attendanceData->toArray()
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * Check device connectivity
     *
     * @return array
     */
    public function checkDeviceConnectivity(): array
    {
        try {
            $deviceInfo = $this->getDeviceInfo();
            
            return [
                'connected' => true,
                'message' => 'Device is connected and responding',
                'device_info' => $deviceInfo->toArray()
            ];
            
        } catch (Exception $e) {
            return [
                'connected' => false,
                'message' => $e->getMessage(),
                'device_info' => null
            ];
        }
    }

    /**
     * Get SDK configuration
     *
     * @return array
     */
    public function getConfiguration(): array
    {
        return [
            'host' => config('easylink.sdk_host'),
            'serial_number' => config('easylink.sdk_sn'),
            'server_port' => config('easylink.server_port'),
            'db_host' => config('easylink.db_host'),
            'db_database' => config('easylink.db_database'),
        ];
    }
}
