<?php

namespace Modules\Fingerspot\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\Fingerspot\Models\DeviceLog;

class DeviceStatusChanged implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $deviceLog;
    public $payload;
    public $timestamp;

    /**
     * Create a new event instance.
     */
    public function __construct(DeviceLog $deviceLog, array $payload)
    {
        $this->deviceLog = $deviceLog;
        $this->payload = $payload;
        $this->timestamp = now()->toDateTimeString();
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('fingerspot.devices'),
            new PrivateChannel('fingerspot.devices.' . $this->deviceLog->device_id),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'device_log' => [
                'id' => $this->deviceLog->id,
                'device_id' => $this->deviceLog->device_id,
                'event_type' => $this->deviceLog->event_type,
                'status' => $this->deviceLog->status,
                'message' => $this->deviceLog->message,
                'timestamp' => $this->deviceLog->timestamp,
            ],
            'payload' => $this->payload,
            'timestamp' => $this->timestamp,
            'event_name' => 'device.status.changed'
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'device.status.changed';
    }
}
