<?php

namespace Modules\Fingerspot\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class WebhookLog extends Model
{
    use HasFactory;

    protected $table = 'fingerspot_webhook_logs';

    protected $fillable = [
        'type',
        'status',
        'url',
        'method',
        'headers',
        'payload',
        'ip_address',
        'user_agent',
        'received_at',
        'processed_at',
        'error_message',
    ];

    protected $casts = [
        'headers' => 'array',
        'payload' => 'array',
        'received_at' => 'datetime',
        'processed_at' => 'datetime',
    ];

    /**
     * Scope for successful webhooks
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', 'received');
    }

    /**
     * Scope for failed webhooks
     */
    public function scopeFailed($query)
    {
        return $query->where('status', '!=', 'received');
    }

    /**
     * Scope for recent webhooks
     */
    public function scopeRecent($query, $days = 7)
    {
        return $query->where('received_at', '>=', now()->subDays($days));
    }

    /**
     * Scope by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get formatted payload
     */
    public function getFormattedPayloadAttribute()
    {
        if (is_array($this->payload)) {
            return json_encode($this->payload, JSON_PRETTY_PRINT);
        }
        
        return $this->payload;
    }

    /**
     * Get formatted headers
     */
    public function getFormattedHeadersAttribute()
    {
        if (is_array($this->headers)) {
            return json_encode($this->headers, JSON_PRETTY_PRINT);
        }
        
        return $this->headers;
    }

    /**
     * Check if webhook was successful
     */
    public function isSuccessful()
    {
        return $this->status === 'received';
    }

    /**
     * Check if webhook failed
     */
    public function isFailed()
    {
        return $this->status !== 'received';
    }
}
