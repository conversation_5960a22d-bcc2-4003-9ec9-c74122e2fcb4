<?php

return [
    'name' => 'EasylinkKangangga',
    'alias' => 'easylinkangga',
    'description' => 'EasyLink SDK Module by Kangangga - Fingerspot EasyLink Device Integration',
    
    /*
    |--------------------------------------------------------------------------
    | EasyLink SDK Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for the EasyLink SDK integration with Fingerspot devices.
    | These values are read from environment variables for security.
    |
    */
    
    'sdk_host' => env('EASYLINK_SDK_HOST', 'http://*************:5005'),
    'sdk_sn' => env('EASYLINK_SDK_SN', ''),
    'server_port' => env('EASYLINK_SERVER_PORT', 7005),
    
    /*
    |--------------------------------------------------------------------------
    | Database Configuration
    |--------------------------------------------------------------------------
    |
    | Database connection settings for EasyLink data storage.
    |
    */
    
    'db_host' => env('EASYLINK_DB_HOST', 'localhost'),
    'db_port' => env('EASYLINK_DB_PORT', 3306),
    'db_database' => env('EASYLINK_DB_DATABASE', 'easylink'),
    'db_username' => env('EASYLINK_DB_USERNAME', 'root'),
    'db_password' => env('EASYLINK_DB_PASSWORD', ''),
    
    /*
    |--------------------------------------------------------------------------
    | Module Settings
    |--------------------------------------------------------------------------
    |
    | General module configuration settings.
    |
    */
    
    'enabled' => env('EASYLINK_MODULE_ENABLED', true),
    'debug' => env('EASYLINK_DEBUG', false),
    'timeout' => env('EASYLINK_TIMEOUT', 30),
    'retry_attempts' => env('EASYLINK_RETRY_ATTEMPTS', 3),
    
    /*
    |--------------------------------------------------------------------------
    | UI/UX Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for the module's user interface.
    |
    */
    
    'dashboard' => [
        'title' => 'EasyLink SDK Dashboard',
        'auto_refresh' => env('EASYLINK_AUTO_REFRESH', true),
        'refresh_interval' => env('EASYLINK_REFRESH_INTERVAL', 30), // seconds
        'theme' => env('EASYLINK_THEME', 'default'),
    ],
    
    /*
    |--------------------------------------------------------------------------
    | API Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for API endpoints and responses.
    |
    */
    
    'api' => [
        'rate_limit' => env('EASYLINK_API_RATE_LIMIT', 60), // requests per minute
        'cache_ttl' => env('EASYLINK_CACHE_TTL', 300), // seconds
        'response_format' => env('EASYLINK_RESPONSE_FORMAT', 'json'),
    ],
];
