<?php

namespace App\Http\Controllers;

use App\Services\AttendanceService;
use Illuminate\Http\JsonResponse;

class EasylinkController extends Controller
{
    public function __construct(
        private AttendanceService $attendanceService
    ) {}

    /**
     * Get device information
     */
    public function getDeviceInfo(): JsonResponse
    {
        try {
            $deviceInfo = $this->attendanceService->getDeviceInfo();
            return response()->json([
                'success' => true,
                'data' => $deviceInfo
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get attendance logs
     */
    public function getAttendanceLogs(): JsonResponse
    {
        $result = $this->attendanceService->getFormattedAttendanceLogs();

        return response()->json($result, $result['success'] ? 200 : 500);
    }

    /**
     * Check device connectivity
     */
    public function checkConnectivity(): JsonResponse
    {
        $result = $this->attendanceService->checkDeviceConnectivity();

        return response()->json($result, $result['connected'] ? 200 : 500);
    }

    /**
     * Get SDK configuration
     */
    public function getConfiguration(): JsonResponse
    {
        $config = $this->attendanceService->getConfiguration();

        return response()->json([
            'success' => true,
            'configuration' => $config
        ]);
    }
}