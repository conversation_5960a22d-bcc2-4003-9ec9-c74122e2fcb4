<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Fingerspot Module Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for the Fingerspot Developer API
    | and Webhook integration module.
    |
    */

    'name' => 'Fingerspot',
    'alias' => 'fingerspot',
    'description' => 'Fingerspot Developer API and Webhook Integration Module',
    'version' => '1.0.0',

    /*
    |--------------------------------------------------------------------------
    | API Configuration
    |--------------------------------------------------------------------------
    */
    'api' => [
        'base_url' => env('FINGERSPOT_API_BASE_URL', 'https://developer.fingerspot.io/api'),
        'version' => env('FINGERSPOT_API_VERSION', 'v1'),
        'timeout' => env('FINGERSPOT_API_TIMEOUT', 30),
        'retry_attempts' => env('FINGERSPOT_API_RETRY_ATTEMPTS', 3),
        'retry_delay' => env('FINGERSPOT_API_RETRY_DELAY', 1000), // milliseconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Authentication Configuration
    |--------------------------------------------------------------------------
    */
    'auth' => [
        'api_key' => env('FINGERSPOT_API_KEY'),
        'secret_key' => env('FINGERSPOT_SECRET_KEY'),
        'client_id' => env('FINGERSPOT_CLIENT_ID'),
        'client_secret' => env('FINGERSPOT_CLIENT_SECRET'),
        'access_token' => env('FINGERSPOT_ACCESS_TOKEN'),
        'refresh_token' => env('FINGERSPOT_REFRESH_TOKEN'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Fingerspot Specific Configuration
    |--------------------------------------------------------------------------
    */
    'fingerspot' => [
        'cloud_id' => env('FINGERSPOT_CLOUDID'),
        'server' => env('FINGERSPOT_SERVER'),
        'server_port' => env('FINGERSPOT_SERVERPORT'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Webhook Configuration
    |--------------------------------------------------------------------------
    */
    'webhook' => [
        'enabled' => env('FINGERSPOT_WEBHOOK_ENABLED', true),
        'secret' => env('FINGERSPOT_WEBHOOK_SECRET'),
        'verify_signature' => env('FINGERSPOT_WEBHOOK_VERIFY_SIGNATURE', true),
        'endpoints' => [
            'attendance' => env('FINGERSPOT_WEBHOOK_ATTENDANCE_ENDPOINT', '/webhook/fingerspot/attendance'),
            'employee' => env('FINGERSPOT_WEBHOOK_EMPLOYEE_ENDPOINT', '/webhook/fingerspot/employee'),
            'device' => env('FINGERSPOT_WEBHOOK_DEVICE_ENDPOINT', '/webhook/fingerspot/device'),
            'general' => env('FINGERSPOT_WEBHOOK_GENERAL_ENDPOINT', '/webhook/fingerspot/general'),
        ],
        'events' => [
            'attendance.created',
            'attendance.updated',
            'attendance.deleted',
            'employee.created',
            'employee.updated',
            'employee.deleted',
            'device.online',
            'device.offline',
            'device.error',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Device Configuration
    |--------------------------------------------------------------------------
    */
    'device' => [
        'default_device_id' => env('FINGERSPOT_DEFAULT_DEVICE_ID'),
        'sync_interval' => env('FINGERSPOT_SYNC_INTERVAL', 300), // seconds
        'auto_sync' => env('FINGERSPOT_AUTO_SYNC', true),
        'batch_size' => env('FINGERSPOT_BATCH_SIZE', 100),
    ],

    /*
    |--------------------------------------------------------------------------
    | Module Settings
    |--------------------------------------------------------------------------
    */
    'enabled' => env('FINGERSPOT_MODULE_ENABLED', true),
    'debug' => env('FINGERSPOT_DEBUG', false),
    'cache_ttl' => env('FINGERSPOT_CACHE_TTL', 300), // seconds
    'rate_limit' => env('FINGERSPOT_RATE_LIMIT', 60), // requests per minute

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    */
    'logging' => [
        'enabled' => env('FINGERSPOT_LOGGING_ENABLED', true),
        'level' => env('FINGERSPOT_LOG_LEVEL', 'info'),
        'channel' => env('FINGERSPOT_LOG_CHANNEL', 'single'),
        'webhook_logs' => env('FINGERSPOT_WEBHOOK_LOGS', true),
        'api_logs' => env('FINGERSPOT_API_LOGS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Database Configuration
    |--------------------------------------------------------------------------
    */
    'database' => [
        'connection' => env('FINGERSPOT_DB_CONNECTION', 'mysql'),
        'table_prefix' => env('FINGERSPOT_TABLE_PREFIX', 'fingerspot_'),
        'store_raw_data' => env('FINGERSPOT_STORE_RAW_DATA', true),
        'cleanup_old_data' => env('FINGERSPOT_CLEANUP_OLD_DATA', true),
        'cleanup_days' => env('FINGERSPOT_CLEANUP_DAYS', 90),
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    */
    'security' => [
        'encrypt_sensitive_data' => env('FINGERSPOT_ENCRYPT_SENSITIVE_DATA', true),
        'allowed_ips' => env('FINGERSPOT_ALLOWED_IPS', ''),
        'webhook_ip_whitelist' => env('FINGERSPOT_WEBHOOK_IP_WHITELIST', ''),
        'api_ip_whitelist' => env('FINGERSPOT_API_IP_WHITELIST', ''),
    ],

    /*
    |--------------------------------------------------------------------------
    | Feature Flags
    |--------------------------------------------------------------------------
    */
    'features' => [
        'real_time_sync' => env('FINGERSPOT_REAL_TIME_SYNC', true),
        'employee_management' => env('FINGERSPOT_EMPLOYEE_MANAGEMENT', true),
        'device_management' => env('FINGERSPOT_DEVICE_MANAGEMENT', true),
        'attendance_reports' => env('FINGERSPOT_ATTENDANCE_REPORTS', true),
        'webhook_retry' => env('FINGERSPOT_WEBHOOK_RETRY', true),
        'api_caching' => env('FINGERSPOT_API_CACHING', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | UI Configuration
    |--------------------------------------------------------------------------
    */
    'ui' => [
        'theme' => env('FINGERSPOT_UI_THEME', 'modern'),
        'auto_refresh' => env('FINGERSPOT_UI_AUTO_REFRESH', true),
        'refresh_interval' => env('FINGERSPOT_UI_REFRESH_INTERVAL', 30), // seconds
        'items_per_page' => env('FINGERSPOT_UI_ITEMS_PER_PAGE', 25),
        'date_format' => env('FINGERSPOT_UI_DATE_FORMAT', 'Y-m-d H:i:s'),
    ],
];
