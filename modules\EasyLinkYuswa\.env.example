# EasyLink Configuration (Shared by both Kangangga and Yuswa modules)
# Add these variables to your main application's .env file (not a separate .env file)
# Both EasyLinkKangangga and EasyLinkYuswa modules use the same configuration variables

# =============================================================================
# Device Configuration (Shared)
# =============================================================================

# EasyLink device host URL (IP address and port)
EASYLINK_SDK_HOST=http://**************:5005

# Device serial number (unique identifier for your EasyLink device)
EASYLINK_SDK_SN=66208023321907

# Device server port
EASYLINK_SERVER_PORT=7005

# =============================================================================
# Database Configuration (Shared)
# =============================================================================

# Database connection settings for the EasyLink device database
EASYLINK_DB_HOST=localhost
EASYLINK_DB_DATABASE=fin_pro_test
EASYLINK_DB_USERNAME=root
EASYLINK_DB_PASSWORD=
EASYLINK_DB_PORT=3306

# =============================================================================
# Module Settings (Shared)
# =============================================================================

# Enable or disable the modules
EASYLINK_MODULE_ENABLED=true

# Enable debug mode for detailed error logging
EASYLINK_DEBUG=false

# Connection timeout in seconds
EASYLINK_TIMEOUT=30

# Auto-refresh dashboard data
EASYLINK_AUTO_REFRESH=true

# Auto-refresh interval in seconds
EASYLINK_REFRESH_INTERVAL=30

# =============================================================================
# Logging Configuration (Shared)
# =============================================================================

# Enable or disable module logging
EASYLINK_LOGGING_ENABLED=true

# Log level (debug, info, warning, error)
EASYLINK_LOG_LEVEL=info

# Log channel (single, daily, stack, etc.)
EASYLINK_LOG_CHANNEL=single

# =============================================================================
# API Settings (Shared)
# =============================================================================

# API rate limit (requests per minute)
EASYLINK_API_RATE_LIMIT=60

# Cache TTL in seconds
EASYLINK_CACHE_TTL=300

# API response format
EASYLINK_RESPONSE_FORMAT=json

# =============================================================================
# Advanced Settings
# =============================================================================

# Maximum retry attempts for failed requests
EASYLINK_YUSWA_MAX_RETRIES=3

# Retry delay in milliseconds
EASYLINK_YUSWA_RETRY_DELAY=1000

# Connection timeout in seconds
EASYLINK_YUSWA_CONNECTION_TIMEOUT=10

# Read timeout in seconds
EASYLINK_YUSWA_READ_TIMEOUT=30
